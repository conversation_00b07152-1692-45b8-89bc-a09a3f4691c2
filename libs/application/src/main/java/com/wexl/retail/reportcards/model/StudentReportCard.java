package com.wexl.retail.reportcards.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "student_report_cards")
public class StudentReportCard extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String orgSlug;

  private Long studentId;

  @Enumerated(EnumType.STRING)
  private StudentReportCardStatus status;

  @Column(columnDefinition = "TEXT")
  private String failureReason;

  @Column(columnDefinition = "TEXT")
  private String stackTrace;

  @ManyToOne(fetch = FetchType.LAZY)
  private ReportCardJob reportCardJob;

  private String reportCardPath;

  private Long reportCardTemplate;

  private String reportCardConfig;

  @Column(name = "otd_id")
  private Long offlineTestDefinitionId;

  @Enumerated(EnumType.STRING)
  private ReportCardTemplateType templateType;
}
