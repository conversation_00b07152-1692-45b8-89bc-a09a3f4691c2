package com.wexl.retail.organization.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class GenericOrgDto {
  private String orgSlug;
  private String orgName;
  private Boolean isPublisher;
  private Boolean isParent;
  private String status;
  private Long startDate;
}
