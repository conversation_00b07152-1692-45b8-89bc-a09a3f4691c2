package com.wexl.retail.communications.logbook.repository;

import com.wexl.retail.communications.logbook.model.LogBook;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface LogBookRepository extends JpaRepository<LogBook, Long> {

  @Query(
      value =
          """
                            SELECT * FROM log_book WHERE org_slug = :orgSlug
                              AND (CAST(:type AS VARCHAR) IS NULL OR type = :type)
                              AND (:boardSlug IS NULL OR board_slug IN (:boardSlug))
                              AND (:gradeSlug IS NULL OR grade_slug IN (:gradeSlug))
                              AND (:sectionUuid IS NULL OR section_uuid IN (:sectionUuid))
                              AND (:studentIds IS NULL OR student_id IN (:studentIds))
                              order by created_at desc""",
      nativeQuery = true)
  List<LogBook> getLongBookData(
      String orgSlug,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      List<Long> studentIds,
      String type);

  List<LogBook> findAllByOrgSlug(String orgSlug);

  List<LogBook> findAllByOrgSlugAndStudentIdOrderByCreatedAtDesc(String orgSlug, Long studentId);
}
