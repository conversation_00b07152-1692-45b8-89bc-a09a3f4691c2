package com.wexl.retail.offlinetest.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.offlinetest.dto.OfflineTestAssessmentDto;
import com.wexl.retail.offlinetest.model.OfflineTestAssessment;
import com.wexl.retail.offlinetest.repository.OfflineTestAssessmentRepository;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class OfflineTestAssessmentService {

  private final OfflineTestAssessmentRepository offlineTestAssessmentRepository;

  public List<OfflineTestAssessmentDto.Response> getOfflineAssessments() {
    return buildResponse(offlineTestAssessmentRepository.findAll());
  }

  private List<OfflineTestAssessmentDto.Response> buildResponse(
      List<OfflineTestAssessment> offlineTestAssessmentList) {
    List<OfflineTestAssessmentDto.Response> responseList = new ArrayList<>();
    if (offlineTestAssessmentList.isEmpty()) {
      return responseList;
    }
    offlineTestAssessmentList.forEach(
        offlineTestAssessment ->
            responseList.add(
                OfflineTestAssessmentDto.Response.builder()
                    .id(offlineTestAssessment.getId())
                    .name(offlineTestAssessment.getName())
                    .slug(offlineTestAssessment.getSlug())
                    .build()));
    return responseList;
  }

  public OfflineTestAssessment validateOfflineTestAssessment(String assessmentSlug) {
    var optionalOfflineTestAssessment = offlineTestAssessmentRepository.findBySlug(assessmentSlug);
    if (optionalOfflineTestAssessment.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Invalid.OfflineTestAssessmentSlug",
          new String[] {assessmentSlug});
    }
    return optionalOfflineTestAssessment.get();
  }
}
