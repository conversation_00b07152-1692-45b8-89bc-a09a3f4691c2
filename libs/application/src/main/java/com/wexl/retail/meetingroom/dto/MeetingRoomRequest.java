package com.wexl.retail.meetingroom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.schedules.domain.MeetingType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class MeetingRoomRequest {
  private String name;
  private String link;
  private MeetingType type;

  @JsonProperty("host_link")
  private String hostLink;

  @JsonProperty("join_link")
  private String joinLink;

  @JsonProperty("display_name")
  private String displayName;
}
