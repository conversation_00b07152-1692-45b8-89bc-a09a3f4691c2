package com.wexl.retail.organization.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

public record OrganizationDto() {
  @Builder
  public record Response(
      @JsonProperty("org_name") String orgName,
      @JsonProperty("no_of_students") Long noOfStudents,
      @JsonProperty("no_of_teachers") Long noOfTeachers,
      @JsonProperty("mlp_conducted") Long mlpConducted,
      @JsonProperty("online_test_conducted") Long onlineTestConducted,
      @JsonProperty("zero_digital_conducted") Long zeroDigitalConducted) {}

  @Builder
  public record SignupRequest(
      @NotNull @JsonProperty("name") String name,
      @NotNull @JsonProperty("mobile_number") String mobileNumber,
      @NotNull @JsonProperty("email") String email,
      @NotNull @JsonProperty("password") String password,
      String board,
      @JsonProperty("captcha") String captchaCode,
      @JsonProperty("referred_by") String referredBy) {}

  @Builder
  public record SignupResponse(Organization organization, User user) {}
}
