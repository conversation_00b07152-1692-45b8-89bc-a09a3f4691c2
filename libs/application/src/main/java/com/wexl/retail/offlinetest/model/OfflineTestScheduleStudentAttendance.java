package com.wexl.retail.offlinetest.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "offline_test_schedule_student_attendance")
public class OfflineTestScheduleStudentAttendance extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offline_test_definition_id")
  private OfflineTestDefinition offlineTestDefinition;

  @Column(name = "student_id")
  private Long studentId;

  @Column(name = "present_days")
  private Long presentDays;

  private String remarks;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offline_test_schedule_id")
  private OfflineTestSchedule offlineTestSchedule;
}
