package com.wexl.retail.meetingroom.repository;

import com.wexl.retail.meetingroom.domain.MeetingRoom;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MeetingRoomRepository extends JpaRepository<MeetingRoom, Long> {

  List<MeetingRoom> findByOrgSlugAndDeletedAtIsNull(String orgSlug);

  List<MeetingRoom> findAllMeetingRoomsByJoinLinkAndOrgSlug(String link, String orgSlug);

  Optional<MeetingRoom> findByIdAndOrgSlugIn(Long meetingRoomId, List<String> orgSlug);

  List<MeetingRoom> findAllMeetingRoomsByIdAndOrgSlug(Long id, String orgSlug);

  Optional<MeetingRoom> findByNameAndOrgSlug(String name, String orgSlug);
}
