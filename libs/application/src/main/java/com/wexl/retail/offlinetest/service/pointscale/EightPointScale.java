package com.wexl.retail.offlinetest.service.pointscale;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class EightPointScale implements PointScale {

  @Override
  public String evaluate(int marks) {
    if (marks >= 91) {
      return "A1";
    } else if (marks >= 81) {
      return "A2";
    } else if (marks >= 71) {
      return "B1";
    } else if (marks >= 61) {
      return "B2";
    } else if (marks >= 51) {
      return "C1";
    } else if (marks >= 41) {
      return "C2";
    } else if (marks >= 33) {
      return "D";
    } else {
      return "E";
    }
  }

  @Override
  public String evaluate(BigDecimal marks) {
    if (marks.compareTo(new BigDecimal(91)) >= 0) {
      return "A1";
    } else if (marks.compareTo(new BigDecimal(81)) >= 0) {
      return "A2";
    } else if (marks.compareTo(new BigDecimal(71)) >= 0) {
      return "B1";
    } else if (marks.compareTo(new BigDecimal(61)) >= 0) {
      return "B2";
    } else if (marks.compareTo(new BigDecimal(51)) >= 0) {
      return "C1";
    } else if (marks.compareTo(new BigDecimal(41)) >= 0) {
      return "C2";
    } else if (marks.compareTo(new BigDecimal(33)) >= 0) {
      return "D";
    } else {
      return "E";
    }
  }

  @Override
  public BigDecimal getMarks(String grade) {
    if (Objects.equals(grade, "A1")) {
      return BigDecimal.valueOf(91L);
    } else if (Objects.equals(grade, "A2")) {
      return BigDecimal.valueOf(81L);
    } else if (Objects.equals(grade, "B1")) {
      return BigDecimal.valueOf(71L);
    } else if (Objects.equals(grade, "B2")) {
      return BigDecimal.valueOf(61L);
    } else if (Objects.equals(grade, "C1")) {
      return BigDecimal.valueOf(51L);
    } else if (Objects.equals(grade, "C2")) {
      return BigDecimal.valueOf(41L);
    } else if (Objects.equals(grade, "D")) {
      return BigDecimal.valueOf(33L);
    } else {
      return BigDecimal.valueOf(20L);
    }
  }

  @Override
  public final List<String> getGradesByPointScale(String pointScale) {
    return List.of("A1", "A2", "B1", "B2", "C1", "C2", "D", "E");
  }
}
