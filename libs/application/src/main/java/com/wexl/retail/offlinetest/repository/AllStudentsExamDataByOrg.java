package com.wexl.retail.offlinetest.repository;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

public interface AllStudentsExamDataByOrg {
  @JsonProperty("fullName")
  String getFullName();

  @JsonProperty("studentId")
  Long getStudentId();

  @JsonProperty("orgSlug")
  String getOrgSlug();

  @JsonProperty("orgName")
  String getOrgName();

  @JsonProperty("gradeSlug")
  String getGradeSlug();

  @JsonProperty("gradeName")
  String getGradeName();

  @JsonProperty("testName")
  String getTestName();

  @JsonProperty("testDefinitionId")
  Long getTestDefinitionId();

  @JsonProperty("subjectSlug")
  String getSubjectSlug();

  @JsonProperty("totalMarks")
  Long getTotalMarks();

  @JsonProperty("testScheduleStudentId")
  Long getTestScheduleStudentId();

  @JsonProperty("marksScored")
  Long getMarksScored();

  @JsonProperty("examId")
  Long getExamId();

  @JsonProperty("sectionName")
  String getSectionName();

  @JsonProperty("scheduleDate")
  LocalDateTime getScheduleDate();
}
