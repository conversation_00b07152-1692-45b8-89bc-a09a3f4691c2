package com.wexl.retail.organization.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.mobile.dto.MobileConfigDto;
import com.wexl.retail.model.Organization;
import java.sql.Date;
import java.sql.Timestamp;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgParentResponse {

  @JsonProperty("name")
  String name;

  @JsonProperty("slug")
  String slug;

  @JsonProperty("created_at")
  Timestamp createdAt;

  @JsonProperty("id")
  Long id;

  @JsonProperty("metadata")
  OrgMetaData metadata;

  @JsonProperty("curriculum")
  Curriculum curriculum;

  @JsonProperty("mobile_config_package_response")
  MobileConfigDto.PackageResponse mobileConfigPackageResponse;

  @JsonProperty("mobile_config_keyvalue_response")
  MobileConfigDto.KeyValueResponse mobileConfigKeyValueResponse;

  @JsonProperty("abbrevation")
  String abbrevation;

  @JsonProperty("is_publisher")
  Boolean isPublisher;

  @JsonProperty("is_parent")
  Boolean isParent;

  @JsonProperty("updated_at")
  Timestamp updatedAt;

  @JsonProperty("deleted_at")
  Date deletedAt;

  @JsonProperty("parent")
  Organization parent;

  @JsonProperty("publisher")
  Organization publisher;

  @JsonProperty("status")
  Boolean status;
}
