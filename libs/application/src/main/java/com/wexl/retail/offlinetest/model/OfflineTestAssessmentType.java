package com.wexl.retail.offlinetest.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "offline_test_assessment_type")
public class OfflineTestAssessmentType extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offline_test_assessment_id")
  private OfflineTestAssessment offlineTestAssessment;

  @Column(name = "name")
  private String name;

  @Column(name = "max_marks")
  private Long maxMarks;

  @Column(name = "pass_percentage")
  private Long passPercentage;

  @Column(name = "seq_no")
  private Long seqNo;
}
