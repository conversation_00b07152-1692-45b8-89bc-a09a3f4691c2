package com.wexl.retail.organization;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@ConditionalOnProperty("app.orgs.deletion.enabled")
public class Org {
  private final OrgDeletionService orgDeletionService;

  @PostMapping("/permanent/delete-orgs")
  public void deleteOrg(@RequestBody OrgDto orgDto) {
    log.info("Deleting orgs {}", orgDto.getOrgs());
    orgDeletionService.deleteOrg(orgDto);
  }
}
