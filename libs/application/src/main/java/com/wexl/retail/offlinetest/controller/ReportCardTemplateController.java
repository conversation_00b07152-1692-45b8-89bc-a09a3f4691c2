package com.wexl.retail.offlinetest.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplateDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.offlinetest.service.ReportCardTemplateService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/report-card-templates")
@RequiredArgsConstructor
public class ReportCardTemplateController {

  private final ReportCardTemplateService reportCardTemplateService;
  private final AuthService authService;
  private final UserRoleHelper userRoleHelper;

  @GetMapping()
  public List<ReportCardTemplateDto.Response> getReportCardTemplates(
      @PathVariable String orgSlug,
      @RequestParam(value = "type") ReportCardTemplateType type,
      @RequestParam(value = "board", required = false) String board,
      @RequestParam(value = "grade", required = false) String grade,
      @RequestParam(value = "childOrg", required = false) String childOrg) {
    return reportCardTemplateService.getReportCardTemplatesByOrg(
        Objects.isNull(childOrg) ? orgSlug : childOrg, type, grade, board);
  }

  @IsOrgAdminOrTeacher
  @PostMapping(value = "/{reportCardTemplateId}/all-students")
  public void generateAllStudentsReportCardTemplate(
      @PathVariable String orgSlug,
      @PathVariable Long reportCardTemplateId,
      @RequestBody ReportCardDto.Request request) {
    reportCardTemplateService.generateReportCardAsync(orgSlug, reportCardTemplateId, request);
  }

  @IsOrgAdminOrTeacher
  @GetMapping(value = "/all-students")
  public String getAllStudentsReportCard(
      @PathVariable String orgSlug,
      @RequestParam(value = "offline_test_definition_id") Long offlineTestDefinitionId) {
    return reportCardTemplateService.getAllStudentsReportCard(orgSlug, offlineTestDefinitionId);
  }
}
