package com.wexl.retail.offlinetest.service.pointscale;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class FivePointScale implements PointScale {
  @Override
  public String evaluate(int marks) {
    if (marks >= 91) {
      return "A+";
    } else if (marks >= 75) {
      return "A";
    } else if (marks >= 60) {
      return "B";
    } else if (marks >= 40) {
      return "C";
    } else {
      return "D";
    }
  }

  @Override
  public String evaluate(BigDecimal marks) {
    if (marks.compareTo(new BigDecimal(91)) >= 0) {
      return "A+";
    } else if (marks.compareTo(new BigDecimal(75)) >= 0) {
      return "A";
    } else if (marks.compareTo(new BigDecimal(60)) >= 0) {
      return "B";
    } else if (marks.compareTo(new BigDecimal(40)) >= 0) {
      return "C";
    } else {
      return "D";
    }
  }

  @Override
  public BigDecimal getMarks(String grade) {
    if (Objects.equals(grade, "A+")) {
      return BigDecimal.valueOf(91L);
    } else if (Objects.equals(grade, "A")) {
      return BigDecimal.valueOf(75L);
    } else if (Objects.equals(grade, "B")) {
      return BigDecimal.valueOf(60L);
    } else if (Objects.equals(grade, "C")) {
      return BigDecimal.valueOf(40L);
    } else return BigDecimal.valueOf(20L);
  }

  @Override
  public final List<String> getGradesByPointScale(String pointScale) {
    return List.of("A+", "A", "B", "C", "D");
  }
}
