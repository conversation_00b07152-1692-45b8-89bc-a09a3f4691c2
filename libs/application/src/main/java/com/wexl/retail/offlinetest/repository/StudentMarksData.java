package com.wexl.retail.offlinetest.repository;

import java.util.Date;

public interface StudentMarksData {
  String getSubjectName();

  Long getScheduleMarks();

  Double getStudentMarks();

  String getTitle();

  Long getSeqNo();

  String getType();

  String getCategory();

  String getAssessmentCategory();

  String getTermSlug();

  Double getTotalMarks();

  Long getSubjectMarks();

  Long getOtdId();

  String getAssessmentSlug();

  String getRemarks();

  Long getStudentId();

  String getStudentName();

  Date getStartDate();

  String getTestName();

  String getSubjectSlug();

  Long getRollNo();

  Long getClassRollNumber();
}
