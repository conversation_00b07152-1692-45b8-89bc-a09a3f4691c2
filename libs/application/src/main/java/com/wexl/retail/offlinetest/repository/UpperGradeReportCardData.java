package com.wexl.retail.offlinetest.repository;

public interface UpperGradeReportCardData {
  String getSubjectName();

  Long getTotal();

  Double getMarks();

  Long getMarks2();

  Long getMarks3();

  String getAssessmentSlug();

  String getTitle();

  String getIsAttended();

  Long getSeqNo();

  String getType();

  Long getOtdId();

  String getCategory();

  String getTermSlug();

  Double getTotalMarks();

  String getRemarks();

  Long getSubjectMarks();

  Double getActualMarks();
}
