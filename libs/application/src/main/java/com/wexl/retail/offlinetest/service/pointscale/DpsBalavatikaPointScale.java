package com.wexl.retail.offlinetest.service.pointscale;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class DpsBalavatikaPointScale implements PointScale {
  @Override
  public String evaluate(int marks) {
    if (marks >= 45) {
      return "O";
    } else if (marks >= 38) {
      return "E";
    } else if (marks >= 28) {
      return "A";
    } else if (marks >= 18) {
      return "D";
    } else if (marks >= 0) {
      return "B";
    } else {
      return "NA";
    }
  }

  @Override
  public String evaluate(BigDecimal marks) {
    if (marks.compareTo(new BigDecimal(45)) >= 0) {
      return "O";
    } else if (marks.compareTo(new BigDecimal(38)) >= 0) {
      return "E";
    } else if (marks.compareTo(new BigDecimal(28)) >= 0) {
      return "A";
    } else if (marks.compareTo(new BigDecimal(18)) >= 0) {
      return "D";
    } else if (marks.compareTo(new BigDecimal(0)) >= 0) {
      return "B";
    } else {
      return "NA";
    }
  }

  @Override
  public BigDecimal getMarks(String grade) {
    if (Objects.equals(grade, "O")) {
      return BigDecimal.valueOf(45L);
    } else if (Objects.equals(grade, "E")) {
      return BigDecimal.valueOf(38L);
    } else if (Objects.equals(grade, "A")) {
      return BigDecimal.valueOf(28L);
    } else if (Objects.equals(grade, "D")) {
      return BigDecimal.valueOf(18L);
    } else if (Objects.equals(grade, "B")) {
      return BigDecimal.valueOf(0L);
    } else {
      return BigDecimal.valueOf(0L);
    }
  }

  @Override
  public List<String> getGradesByPointScale(String pointScale) {
    return List.of("O", "E", "A", "D", "B");
  }
}
