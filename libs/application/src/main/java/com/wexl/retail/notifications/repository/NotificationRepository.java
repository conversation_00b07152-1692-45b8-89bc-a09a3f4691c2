package com.wexl.retail.notifications.repository;

import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.communications.homework.service.HomeworkDetails;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.notifications.model.Notification;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {
  Optional<Notification> findByIdAndOrgSlug(Long id, String orgSlug);

  @Query(
      value =
          """
                           select n.* from  public.notifications n
                           inner join notification_students ns on ns.notification_id = n.id
                           left join  message_templates mt  on mt.id  = n.message_template_id or mt.message_template_category  = n.category_id
                           left join message_templates_category mtc  on mtc.id  = mt.message_template_category
                           where ns.student_id = :studentId and ns.org_slug in(:orgSlug)
                           and (cast((:fromDate) as varChar) is null or to_char(n.created_at,'yyyy-MM-dd') >=:fromDate)
                           and (cast((:appreciationMsg) as varChar) is null or n.title =(:appreciationMsg))
                           and (cast(:categoryId as varchar) is null or message_template_category =:categoryId)
                           and n.notification_type not in (15)
                           order by n.created_at DESC limit :limit""",
      nativeQuery = true)
  List<Notification> getStudentNotificationsByIdAndOrgSlug(
      List<String> orgSlug,
      Long studentId,
      String fromDate,
      String appreciationMsg,
      Long categoryId,
      Long limit);

  List<Notification> findAllByCreatedByAndOrgSlug(Teacher teacher, String orgSlug);

  @Query(
      value =
          """
                  select * from  public.notifications
                                                    where teacher_id = :teacherId  and org_slug =:orgSlug and title =:appreciationMessage
                                                    order by created_at DESC limit :limit""",
      nativeQuery = true)
  List<Notification> getTeacherAppreciationNotificationsByIdAndOrgSlug(
      String orgSlug, Long teacherId, Integer limit, String appreciationMessage);

  @Query(
      value =
          """
                          select n.* from  notifications n
                                              inner join notification_teachers nt on nt.notification_id = n.id
                                              where nt.teacher_id = :teacherId  and nt.org_slug  = :orgSlug and title =:appreciationMessage
                                              order by nt.id desc
                          """,
      nativeQuery = true)
  List<Notification> getTeacherAppreciationNotificationsToAdmin(
      Long teacherId, String orgSlug, String appreciationMessage);

  @Query(
      value =
          """
                  select n.* from  notifications n
                    left join  message_templates mt  on mt.id  = n.message_template_id
                     join  message_templates_category mtc  on mt.message_template_category = mtc.id or mtc.id = n.category_id
                     where teacher_id = :teacherId and n.org_slug = :orgSlug
                     and (cast(:categoryId as varchar) is null or mtc.id =:categoryId)
                     order by n.created_at DESC limit :limit""",
      nativeQuery = true)
  List<Notification> getTeacherNotificationsByIdAndOrgSlug(
      String orgSlug, Long teacherId, Long categoryId, Integer limit);

  @Query(
      value =
          """
                  select n.* from  notifications n
                    inner join notification_teachers nt on nt.notification_id = n.id
                    left join  message_templates mt  on mt.id  = n.message_template_id
                    left join message_templates_category mtc  on mtc.id  = mt.message_template_category
                    where nt.teacher_id = :teacherId  and nt.org_slug  = :orgSlug
                    and  (cast(:categoryId as varchar) is null or mtc.id =:categoryId)
                    order by nt.id desc limit :limit""",
      nativeQuery = true)
  List<Notification> getTeacherNotificationsToAdmin(
      Long teacherId, String orgSlug, Long categoryId, Integer limit);

  @Query(
      value =
          """
                          select n.* from  notifications n
                             join  message_templates mt  on mt.id  = n.message_template_id
                             join message_templates_category mtc  on mtc.id  = mt.message_template_category
                             where teacher_id = :teacherId  and (cast(:categoryId as varchar) is null or mtc.id =:categoryId)
                             order by n.created_at DESC""",
      nativeQuery = true)
  List<Notification> findByByTeacherIdAndCategory(Long teacherId, Long categoryId);

  List<Notification> findAllByCreatedBy(Teacher teacher);

  @Query(
      value =
          """
                  select count(*) from notifications n
                  join notification_sections ns on ns.notification_id =n.id
                  where n.org_slug =:orgSlug and n.deleted_at is null
                  """,
      nativeQuery = true)
  Long getCountOfNotificationsByOrgSlug(String orgSlug);

  @Query(
      value =
          """
                  select count(*) from notifications n
                  join notification_sections ns on ns.notification_id =n.id
                  join sections s on ns.section_id =s.id
                  where n.org_slug =:orgSlug and s.grade_slug =:gradeSlug and n.deleted_at is null
                  """,
      nativeQuery = true)
  Long getCountOfNotificationsByGradeSlug(String orgSlug, String gradeSlug);

  @Query(
      value =
          """
                  SELECT COUNT(*)
                  FROM notifications n
                  JOIN notification_sections ns ON ns.notification_id = n.id
                  JOIN sections s ON ns.section_id = s.id
                  WHERE n.org_slug = :orgSlug
                    AND s.grade_slug = :gradeSlug
                    AND n.deleted_at IS NULL
                    AND n.category_id = :messageTemplateCategoryId
                  """,
      nativeQuery = true)
  long getNotificationsCountByCategory(
      String orgSlug, String gradeSlug, Long messageTemplateCategoryId);

  @Query(
      value =
          """
                          SELECT COUNT(*)
                          FROM notifications n
                          JOIN notification_sections ns ON ns.notification_id = n.id
                          JOIN sections s ON ns.section_id = s.id
                          WHERE n.org_slug = :orgSlug
                            AND s.grade_slug = :gradeSlug
                            AND n.deleted_at IS NULL
                            AND n.message_template_id in(:messageTemplateIds)
                            AND n.category_id is null
                          """,
      nativeQuery = true)
  long getNotificationsCountByTemplates(
      String orgSlug, String gradeSlug, List<Long> messageTemplateIds);

  List<Notification> findAllByCreatedByAndOrgSlugAndFeatureOrderByCreatedAtDesc(
      Teacher Teacher, String orgSlug, CommunicationFeature feature);

  List<Notification> findAllByOrgSlugAndFeatureOrderByCreatedAtDesc(
      String orgSlug, CommunicationFeature feature);

  List<Notification>
      findByOrgSlugAndFeatureAndStudentNotifications_StudentAndDeletedAtIsNullOrderByCreatedAtDesc(
          String orgSlug, CommunicationFeature feature, Student student);

  @Query(
      value =
          """
          select n.* from   notifications n
          left join notification_students ns  on ns.notification_id = n.id
          where n.org_slug = :orgSlug
          and  feature in (:features) and ns.student_id = :student
          and to_char(n.created_at,'yyyy-MM-dd') between :fromDate and :toDate
          and n.deleted_at  is null order by created_at desc""",
      nativeQuery = true)
  List<Notification> getNotificationsByFeaturesAndStudent(
      String orgSlug, Long student, String fromDate, String toDate, List<String> features);

  @Query(
      value =
          """
                  select n.* from   notifications n
                  left join notification_students ns  on ns.notification_id = n.id
                  where n.org_slug = :orgSlug
                  and ns.student_id = :student
                  and n.from_date::date >= cast(:fromDate as date) and n.to_date::date <= cast(:toDate as date)
                  and n.deleted_at  is null order by created_at desc""",
      nativeQuery = true)
  List<Notification> getStudentNotificationsByFromDateAndToDate(
      String orgSlug, Long student, String fromDate, String toDate);

  @Query(
      value =
          """
              select n.* from  notifications n
               left join notification_teachers ts  on ts.notification_id = n.id
               where n.org_slug = :orgSlug
               and (cast((:teacherId) as varChar) is null or n.teacher_id  =(:teacherId))
               and to_char(n.created_at,'yyyy-MM-dd') between :fromDate and :toDate
               and n.deleted_at  is null order by created_at desc""",
      nativeQuery = true)
  List<Notification> getNotificationsByFeaturesAndTeacher(
      String orgSlug, Long teacherId, String fromDate, String toDate);

  @Query(
      value =
          """
                      select n.* from  notifications n
                       left join notification_teachers ts  on ts.notification_id = n.id
                       where n.org_slug = :orgSlug
                       and (cast((:teacherId) as varChar) is null or n.teacher_id  =(:teacherId))
                       and n.from_date::date >= cast(:fromDate as date) and n.to_date::date <= cast(:toDate as date)
                       and n.deleted_at  is null order by created_at desc""",
      nativeQuery = true)
  List<Notification> getTeacherNotificationsByFromDateAndToDate(
      String orgSlug, Long teacherId, String fromDate, String toDate);

  @Query(
      value =
          """
                          select distinct
                              concat(u.first_name, ' ', u.last_name) as teacherName,
                              n.created_at as date,
                              n.title as title,
                              n.message as message,
                              s2."name" as section,
                              n.attachments::text as attachments
                          from notifications n
                          join teacher_details td on n.teacher_id = td.id
                          join users u on u.id = td.user_id
                          join notification_students ns on ns.notification_id = n.id
                          join students s on s.id = ns.student_id
                          join sections s2 on s2.id = s.section_id
                          where n.feature = :feature and n.org_slug = :orgSlug and n.created_at between :fromDate and :toDate
                             """,
      nativeQuery = true)
  List<HomeworkDetails> getAllOrgNotificationsByCommunicationFeature(
      String orgSlug, String feature, Timestamp fromDate, Timestamp toDate);
}
