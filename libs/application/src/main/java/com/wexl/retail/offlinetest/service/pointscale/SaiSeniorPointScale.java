package com.wexl.retail.offlinetest.service.pointscale;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class SaiSeniorPointScale implements PointScale {

  @Override
  public String evaluate(int marks) {
    if (marks < 35) {
      return "Fail";
    }
    return "Pass";
  }

  @Override
  public String evaluate(BigDecimal marks) {
    if (marks.compareTo(new BigDecimal(35)) < 0) {
      return "Fail";
    }
    return "Pass";
  }

  @Override
  public BigDecimal getMarks(String grade) {
    if (Objects.equals(grade, "Fail")) {
      return new BigDecimal(34);
    }
    return new BigDecimal(80);
  }

  @Override
  public List<String> getGradesByPointScale(String pointScale) {
    return Collections.emptyList();
  }
}
