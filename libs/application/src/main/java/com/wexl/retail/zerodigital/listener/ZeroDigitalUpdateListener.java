package com.wexl.retail.zerodigital.listener;

import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.publisher.ExamCompletionEvent;
import com.wexl.retail.zerodigital.service.ZeroDigitalService;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class ZeroDigitalUpdateListener implements ApplicationListener<ExamCompletionEvent> {

  private final ZeroDigitalService zeroDigitalService;

  @Override
  public void onApplicationEvent(ExamCompletionEvent examCompletionEvent) {
    Object source = examCompletionEvent.getSource();
    if (source instanceof Exam exam
        && Objects.nonNull(exam.getScheduleTest())
        && exam.getScheduleTest().getIsDac()) {
      zeroDigitalService.updateZeroDigitalStatus(exam);
    }
  }
}
