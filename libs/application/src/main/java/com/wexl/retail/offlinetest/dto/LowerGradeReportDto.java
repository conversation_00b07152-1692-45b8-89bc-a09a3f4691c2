package com.wexl.retail.offlinetest.dto;

import java.util.List;
import lombok.Builder;

public record LowerGradeReportDto() {
  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String dateOfBirth,
      String orgSlug,
      String gradeSlug,
      String boardSlug,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTable thirdTable,
      List<FourthTable> fourthTable,
      Attendance attendance,
      GradeTable gradeTable,
      String gradingScale) {}

  @Builder
  public record GradeTable(String title) {}

  @Builder
  public record FirstTable(
      String title,
      String column1,
      String column2,
      String column3,
      String column4,
      String column5,
      String column6,
      String column7,
      String column8,
      String column9,
      String column10,
      String column11,
      List<Marks> marks,
      List<String> subjectGradeSlug,
      List<Marks> external,
      Totals totals) {}

  @Builder
  public record SecondTable(String title, List<SecondTableMarks> marks) {}

  @Builder
  public record ThirdTable(List<ThirdTableMarks> marks) {}

  @Builder
  public record FourthTable(String term1Grade, String term2Grade) {}

  @Builder
  public record SecondTableMarks(String subjectName, String term1Grade, String term2Grade) {}

  @Builder
  public record ThirdTableMarks(
      Long sno,
      String subject,
      String term1grade,
      String term1description,
      String term2grade,
      String term2description) {}

  @Builder
  public record Marks(
      Long sno,
      String subject,
      String pa1,
      String pa2,
      String hye,
      String term1total,
      double term1totalMarks,
      double term2totalMarks,
      double graphTerm1TotalMarks,
      double graphTerm2TotalMarks,
      String pa3,
      String pa4,
      String ye,
      String term2total,
      String termTotal,
      String pasTotal,
      String hyeYe,
      String overall,
      String overAllGrade,
      Double overAllScored,
      Double overAllExamMarks,
      String grade1,
      String grade2,
      Long seqNo,
      Long otdId,
      String term1grade,
      String term1description,
      String term2grade,
      String term2description) {}

  @Builder
  public record Totals(
      Long annualExam, Double total, String grade, String overallPercentage, String overAllGrade) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, Double attendancePercentage, String remarks) {}

  @Builder
  public record TableMarks(
      List<Marks> firstTableMarks,
      List<Marks> externalMarks,
      List<SecondTableMarks> secondTableMarks,
      List<ThirdTableMarks> thirdTableMarks) {}
}
