package com.wexl.retail.offlinetest.repository;

import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportCardTemplateRepository extends JpaRepository<ReportCardTemplate, Long> {

  List<ReportCardTemplate> findByReportCardTemplateTypeAndOrgSlug(
      ReportCardTemplateType type, String orgSlug);

  List<ReportCardTemplate> findByOrgSlug(String orgSlug);

  Optional<ReportCardTemplate> findByOrgSlugAndReportCardTemplateTypeAndConfig(
      String orgSlug, ReportCardTemplateType type, String config);

  @Query(
      value =
          """
          select rct.* from report_card_templates rct
          left join grade_report_card_templates grct on grct.report_card_template_id = rct.id
          where  grct.grade_slug =:gradeSlug and grct.board_slug =:boardSlug  and
          rct.org_slug =:orgSlug and rct.report_card_template_type =:templateType""",
      nativeQuery = true)
  List<ReportCardTemplate> getTemplateByGradeSlugAndType(
      String orgSlug, String templateType, String boardSlug, String gradeSlug);

  @Query(
      value =
          """
            select rct.* from report_card_templates rct join grade_report_card_templates grct
               on grct.report_card_template_id = rct.id  where grct.grade_slug = :gradeSlug  and grct.board_slug = :boardSlug and org_slug = :orgSlug
               and rct.report_card_template_type = :templateType order by  grct.seq_no nulls last""",
      nativeQuery = true)
  List<ReportCardTemplate> getTemplateByOrgGradeSlugAndBoardAndType(
      String orgSlug, String templateType, String gradeSlug, String boardSlug);

  @Query(
      value =
          """
                  select rct.id from report_card_templates rct
                  left join grade_report_card_templates grct on grct.report_card_template_id = rct.id
                  where grct.grade_slug =:gradeSlug and  grct.board_slug = :boardSlug and
                  rct.org_slug =:orgSlug and rct.report_card_template_type =:templateType and rct.config =:config
                  """,
      nativeQuery = true)
  Long getEYTemplateByGradeSlugAndType(
      String orgSlug, String templateType, String boardSlug, String gradeSlug, String config);

  Optional<ReportCardTemplate> findByReportCardTemplateTypeAndConfig(
      ReportCardTemplateType reportCardTemplateType, String config);

  @Query(
      value =
          """
                          SELECT rct.*
                          FROM report_card_templates rct
                          WHERE rct.report_card_template_type = :reportCardTemplateType
                            AND rct."name" = :name
                            AND (rct.org_slug = :orgSlug OR rct.org_slug = 'wexl-internal')
                          ORDER BY
                           CASE
                              WHEN rct.org_slug = :orgSlug THEN 1 ELSE 2 END;
                          """,
      nativeQuery = true)
  List<ReportCardTemplate> findAllReportCardTemplateTypeName(
      String reportCardTemplateType, String name, String orgSlug);

  Optional<ReportCardTemplate> findByOrgSlugAndConfig(String orgId, String config);

  Optional<ReportCardTemplate> findByIdAndOrgSlug(Long reportCardTemplateId, String orgSlug);
}
