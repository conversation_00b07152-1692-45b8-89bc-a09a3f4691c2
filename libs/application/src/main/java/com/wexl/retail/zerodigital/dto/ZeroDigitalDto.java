package com.wexl.retail.zerodigital.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.zerodigital.model.ZeroDigitalStatus;
import lombok.Builder;

public record ZeroDigitalDto() {

  public record ZdRequest(String board, String grade, String section, String subject) {}

  public record ZdUpdateRequest(
      Long plannedDate,
      @JsonProperty("exam_completed") Boolean examCompleted,
      @JsonProperty("worksheet_issued") Boolean workSheetIssued,
      @JsonProperty("badges_issued") Boolean badgesIssued,
      @JsonProperty("worksheet_completed") Boolean workSheetCompleted) {}

  @Builder
  public record ZeroDigitalReportResponse(
      @JsonProperty("id") Long zdId,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("exam_conducted_at") Long examConductedAt,
      @JsonProperty("is_exam_conducted") Boolean isExamConductedAt,
      @JsonProperty("work_sheet_issued_at") Long workSheetIssuedAt,
      @JsonProperty("is_work_sheet_issued") Boolean isWorkSheetIssuedAt,
      @JsonProperty("badges_issues_at") Long badgesIssuedAt,
      @JsonProperty("is_badge_issued") Boolean isBadgesIssuedAt,
      @JsonProperty("work_sheets_completed") Long workSheetsCompleted,
      @JsonProperty("is_worksheet_completed") Boolean isWorkSheetsCompleted) {}

  @Builder
  public record ZeroDigitalResponse(
      Long zdId,
      String chapterName,
      String chapterSlug,
      Long testScheduleId,
      String associatedTest,
      String workSheet,
      String QuestionResponsePath,
      String leaderBoardLink,
      ZeroDigitalStatus status,
      Long completionDate,
      Long plannedDate,
      String gradeName,
      String subjectName,
      Long testDefinitionId,
      Long examConductedAt,
      Long WorkSheetIssuedAt,
      Long BadgesIssuedAt,
      Long workSheetsCompleted) {}
}
