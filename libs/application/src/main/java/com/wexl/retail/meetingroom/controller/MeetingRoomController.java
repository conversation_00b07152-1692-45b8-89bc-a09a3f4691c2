package com.wexl.retail.meetingroom.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.meetingroom.dto.MeetingRoomRequest;
import com.wexl.retail.meetingroom.dto.MeetingRoomResponse;
import com.wexl.retail.meetingroom.service.MeetingRoomService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/meeting-rooms")
public class MeetingRoomController {

  private final MeetingRoomService meetingRoomService;

  @IsOrgAdmin
  @GetMapping
  public List<MeetingRoomResponse> getAllMeetingRooms(@PathVariable String orgSlug) {

    return meetingRoomService.getAllMeetingResponses(orgSlug);
  }

  @PostMapping
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void createMeetingRequest(
      @PathVariable String orgSlug, @RequestBody MeetingRoomRequest meetingRoomRequest) {

    meetingRoomService.createRequest(meetingRoomRequest, orgSlug);
  }

  @PostMapping("/{roomId}")
  public ResponseEntity<Void> updateMeetingRequest(
      @PathVariable String orgSlug,
      @PathVariable("roomId") Long id,
      @RequestBody MeetingRoomRequest meetingRoomRequest) {
    try {
      meetingRoomService.updateRequest(meetingRoomRequest, orgSlug, id);

    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.Meeting.CreateRequest", e);
    }
    return ResponseEntity.ok().build();
  }

  @DeleteMapping("/{roomId}")
  public ResponseEntity<Void> deleteMeetingRequest(
      @PathVariable String orgSlug, @PathVariable("roomId") Long id) {
    meetingRoomService.deleteRequest(orgSlug, id);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/{roomId}")
  public MeetingRoomResponse getMeetingRoomById(
      @PathVariable String orgSlug, @PathVariable("roomId") Long id) {

    return meetingRoomService.getMeetingRoomResponses(id, orgSlug);
  }
}
