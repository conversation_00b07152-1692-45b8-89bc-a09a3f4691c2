package com.wexl.retail.organization.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ServicesCount {

  private Integer teachersCount;
  private Integer studentsCount;
  private Integer timeSpent;
  private Integer testsCount;
  private Integer assignmentsCount;
  private Integer worksheetsCount;
  private Integer mlpsCount;
  private Integer mlpsCountYesterdayByDate;
  private Integer mlpsCountDayBeforeYesterdayByDate;
  private List<MlpStudentActivity> mlpStudentActivities;
  private List<MlpTeacherActivity> mlpTeacherActivities;
  private String orgName;
  private String orgSlug;
  private Attributes attributes;
}
