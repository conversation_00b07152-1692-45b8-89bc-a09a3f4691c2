package com.wexl.retail.calenderevent.repository;

import com.wexl.retail.calenderevent.dto.CalenderEventType;
import com.wexl.retail.calenderevent.model.CalendarEvent;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CalenderEventRepository extends JpaRepository<CalendarEvent, Long> {

  List<CalendarEvent> findAllByOrgSlugAndAcademicYearSlugAndDateBetween(
      String orgSlug, String academicYearSlug, LocalDateTime startDate, LocalDateTime endDate);

  List<CalendarEvent> findAllByOrgSlugAndAcademicYearSlugAndTypeAndDateBetween(
      String orgSlug,
      String latestAcademicYear,
      CalenderEventType type,
      LocalDateTime startDate,
      LocalDateTime endDate);

  @Query(
      value =
          """
          SELECT ce.* FROM calender_events ce
          WHERE DATE(ce.due_date) BETWEEN :startDate AND :endDate
          AND ce.academic_year_slug = :academicYear
          AND "type" = 1
          AND org_slug = :orgSlug
          AND (cast((:boardSlug) as varChar) IS NULL OR ce.board_slug IN (:boardSlug))
          AND (cast((:gradeSlug) as varChar) is null OR ce.grade_slug IN (:gradeSlug))
          AND (cast((:sectionUuid) as varChar) IS NULL OR ce.section_uuid IN (:sectionUuid))
          """,
      nativeQuery = true)
  List<CalendarEvent> getAllLessonPlannerDataByDate(
      String orgSlug,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      String academicYear,
      LocalDateTime startDate,
      LocalDateTime endDate);

  @Query(
      value =
          """
         SELECT ce.*
               FROM calender_events ce
               WHERE DATE(ce.due_date) BETWEEN :startDate AND :endDate
                 AND ce.academic_year_slug = :academicYear
                 AND "type" = 0
                 AND ce.org_slug = :orgSlug
                 AND (:boardSlug IS NULL OR ce.board_slug IN (:boardSlug))
                 AND (:gradeSlug IS NULL OR ce.grade_slug = :gradeSlug)
                 ORDER BY ce.created_at DESC

    """,
      nativeQuery = true)
  List<CalendarEvent> getAllEventsDataByDate(
      String orgSlug,
      List<String> boardSlug,
      String gradeSlug,
      String academicYear,
      LocalDateTime startDate,
      LocalDateTime endDate);

  List<CalendarEvent> findBySectionUuidInAndOrgSlug(List<String> sectionUuid, String organization);
}
