package com.wexl.retail.organization.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class UserAnalyticsResponse {

  @JsonProperty("active_users")
  private UserAnalytics active;

  @JsonProperty("inactive_users")
  private UserAnalytics inactive;

  @JsonProperty("total_users")
  private UserAnalytics total;

  @JsonProperty("active_students_percentage")
  private double activeStudentsPercentage;

  @JsonProperty("active_teachers_percentage")
  private double activeTeachersPercentage;

  @JsonProperty("active_otherUsers_Percentage")
  private double activeOtherUsersPercentage;
}
