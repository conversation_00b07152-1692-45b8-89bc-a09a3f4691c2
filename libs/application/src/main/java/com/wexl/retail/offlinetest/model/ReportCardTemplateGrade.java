package com.wexl.retail.offlinetest.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "grade_report_card_templates")
public class ReportCardTemplateGrade extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "grade_name")
  private String gradeName;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "board_name")
  private String boardName;

  @Column(name = "board_slug")
  private String boardSlug;

  private Integer seqNo;

  @ManyToOne(fetch = FetchType.LAZY)
  private ReportCardTemplate reportCardTemplate;

  @Column(name = "show_student")
  private Boolean showStudent = false;
}
