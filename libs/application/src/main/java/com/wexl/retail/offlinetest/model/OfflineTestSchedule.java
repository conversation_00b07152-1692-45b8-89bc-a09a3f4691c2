package com.wexl.retail.offlinetest.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "offline_test_schedule")
public class OfflineTestSchedule extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "subject_name")
  private String subjectName;

  private Long marks;

  @Column(name = "scheduled_at")
  private LocalDateTime scheduledAt;

  @Column(name = "exam_start_time")
  private LocalTime examStartTime;

  @Column(name = "exam_end_time")
  private LocalTime examEndTime;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "offline_test_schedule_id")
  private List<OfflineTestScheduleStudent> offlineTestScheduleStudents;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offline_test_definition_id")
  private OfflineTestDefinition offlineTestDefinition;

  @Column(name = "published_at")
  private LocalDateTime publishedAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "subject_metadata_id")
  private SubjectsMetaData subjectsMetaData;

  @Column(name = "show_rc")
  private Boolean showRc;

  @Column(name = "consider_percentage")
  private Boolean considerPercentage;
}
