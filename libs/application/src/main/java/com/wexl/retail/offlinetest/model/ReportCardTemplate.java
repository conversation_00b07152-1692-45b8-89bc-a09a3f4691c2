package com.wexl.retail.offlinetest.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "report_card_templates")
public class ReportCardTemplate extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "name")
  private String name;

  @Column(name = "report_card_template_type")
  @Enumerated(EnumType.STRING)
  private ReportCardTemplateType reportCardTemplateType;

  @Column(name = "config")
  private String config;

  @Column(name = "org_slug")
  private String orgSlug;
}
