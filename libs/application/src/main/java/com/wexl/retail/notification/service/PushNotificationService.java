package com.wexl.retail.notification.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.notification.dto.TimeBombRequest;
import com.wexl.retail.util.AccessToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class PushNotificationService {

  private final String url;
  private final RestTemplate restTemplate;
  private final AccessToken accessToken;
  private final AuthService authService;

  public PushNotificationService(
      @Value("${app.batch.url}") String url,
      RestTemplate restTemplate,
      AccessToken accessToken,
      AuthService authService) {
    this.url = url;
    this.restTemplate = restTemplate;
    this.accessToken = accessToken;
    this.authService = authService;
  }

  public void createTimeBombEntry(TimeBombRequest timeBombRequest) {
    String endPoint = url + "/timebomb";
    var response =
        restTemplate.exchange(
            endPoint,
            HttpMethod.POST,
            getRequestEntity(accessToken.generateAdminToken(authService), timeBombRequest),
            Void.class);

    if (!response.getStatusCode().is2xxSuccessful()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TimeBomb.Request");
    }
  }

  private HttpEntity<TimeBombRequest> getRequestEntity(
      String bearerToken, TimeBombRequest timeBombRequest) {
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    return new HttpEntity<>(timeBombRequest, headers);
  }
}
