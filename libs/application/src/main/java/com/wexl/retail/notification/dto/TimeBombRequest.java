package com.wexl.retail.notification.dto;

import com.wexl.retail.notification.model.AvailableTimeBombJob;
import com.wexl.retail.notification.model.ReferenceType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimeBombRequest {
  @NotEmpty String id;
  @NotNull ReferenceType type;
  @NotNull AvailableTimeBombJob jobToRun;
  @NotNull Instant expiredAt;
  Map<String, Object> jobParams;
}
