package com.wexl.retail.calenderevent.controller;

import com.wexl.retail.calenderevent.dto.CalenderEventDto;
import com.wexl.retail.calenderevent.dto.CalenderEventType;
import com.wexl.retail.calenderevent.service.CalenderEventsService;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class CalenderEventController {

  private final CalenderEventsService calenderEventsService;

  @IsOrgAdmin
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/calender-events:lp")
  public void saveLessonPlanner(
      @PathVariable String orgSlug, @RequestBody CalenderEventDto.LessonPlannerRequest request) {
    calenderEventsService.saveLessonPlanner(orgSlug, request);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/calender-events/{calenderEventId}")
  public void updateLessonPlanner(
      @PathVariable String orgSlug,
      @PathVariable("calenderEventId") long calenderEventId,
      @RequestBody CalenderEventDto.LessonPlannerRequest request) {
    calenderEventsService.updateLessonPlanner(orgSlug, calenderEventId, request);
  }

  @IsOrgAdminOrTeacher
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/calender-events")
  public void saveEvent(
      @PathVariable String orgSlug, @RequestBody CalenderEventDto.EventRequest request) {
    calenderEventsService.saveEvent(orgSlug, request);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/calender-events")
  public List<CalenderEventDto.Response> getCalenderDataByDate(
      @PathVariable String orgSlug,
      @RequestParam("start_date") Long startDate,
      @RequestParam("end_date") Long endDate,
      @RequestParam(value = "type", required = false) CalenderEventType type,
      @RequestParam(value = "board", required = false) List<String> board,
      @RequestParam(value = "grade", required = false) List<String> grade,
      @RequestParam(value = "section", required = false) List<String> section) {
    return calenderEventsService.getCalenderDataByDate(
        orgSlug, startDate, endDate, type, grade, board, section);
  }

  @IsOrgAdmin
  @GetMapping("/calender-events/{calenderEventId}")
  public List<CalenderEventDto.CalenderUserResponse> getCalenderUserData(
      @PathVariable Long calenderEventId) {
    return calenderEventsService.getCalenderUserData(calenderEventId);
  }

  @IsOrgAdminOrTeacher
  @DeleteMapping("/calender-events/{calenderEventId}")
  public void deleteCalenderData(@PathVariable Long calenderEventId) {
    calenderEventsService.deleteCalenderData(calenderEventId);
  }

  @IsTeacher
  @GetMapping("/calender-events/teachers/{teacherAuthId}")
  public List<CalenderEventDto.Response> getTeacherCalenderData(
      @PathVariable String orgSlug,
      @RequestParam("start_date") Long startDate,
      @RequestParam("end_date") Long endDate,
      @RequestParam(value = "board_slug", required = false) String boardSlug,
      @PathVariable String teacherAuthId) {
    return calenderEventsService.getTeacherCalenderData(
        orgSlug, startDate, endDate, teacherAuthId, boardSlug);
  }

  @IsStudent
  @GetMapping("/calender-events/student/{studentAuthId}")
  public List<CalenderEventDto.Response> getStudentCalenderData(
      @PathVariable String orgSlug,
      @RequestParam("start_date") Long startDate,
      @RequestParam("end_date") Long endDate,
      @PathVariable String studentAuthId) {
    return calenderEventsService.getStudentCalenderData(orgSlug, startDate, endDate, studentAuthId);
  }

  @GetMapping("/calender-events:dates")
  public List<CalenderEventDto.CalenderResponse> getCalenderDatesByUser(
      @PathVariable String orgSlug,
      @RequestParam("from_date") Long fDate,
      @RequestParam("to_date") Long tDate) {
    return calenderEventsService.getCalenderDatesByUser(orgSlug, fDate, tDate);
  }

  @IsStudent
  @GetMapping("/students/{studentAuthId}/communications-activities")
  public CalenderEventDto.StudentCommunicationResponse getStudentCalendarActivities(
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @RequestParam("fromDate") Long fromDate,
      @RequestParam("toDate") Long toDate) {
    return calenderEventsService.getStudentCalendarActivities(
        orgSlug, studentAuthId, fromDate, toDate);
  }

  @IsTeacher
  @GetMapping("/teachers/{teacherAuthId}/communications-activities")
  public CalenderEventDto.StudentCommunicationResponse getTeacherCalendarActivities(
      @PathVariable String orgSlug,
      @PathVariable String teacherAuthId,
      @RequestParam("fromDate") Long fromDate,
      @RequestParam("toDate") Long toDate) {
    return calenderEventsService.getTeacherCalendarActivities(
        orgSlug, teacherAuthId, fromDate, toDate);
  }
}
