package com.wexl.retail.reportcards.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "report_card_jobs")
public class ReportCardJob extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  private ReportCardConfig reportCardConfig;

  private ReportCardConfigDto.ReportCardJobStatus status;

  @Column(columnDefinition = "TEXT")
  private String errorMessage;

  @Column(name = "failure_reason", columnDefinition = "VARCHAR(5000)")
  private String failureReason;

  @Column(name = "org_slug")
  private String orgSlug;
}
