package com.wexl.retail.offlinetest.repository;

import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.model.ReportCardTemplateGrade;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportCardTemplateGradeRepository
    extends JpaRepository<ReportCardTemplateGrade, Long> {

  List<ReportCardTemplateGrade> findByBoardSlugAndGradeSlugAndReportCardTemplateIn(
      String boardSlug, String gradeSlug, List<ReportCardTemplate> reportCardTemplates);
}
