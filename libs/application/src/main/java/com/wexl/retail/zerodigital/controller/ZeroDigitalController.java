package com.wexl.retail.zerodigital.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.zerodigital.dto.ZeroDigitalDto;
import com.wexl.retail.zerodigital.service.ZeroDigitalService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/orgs/{orgSlug}/zero-digital")
public class ZeroDigitalController {

  private final ZeroDigitalService zeroDigitalService;

  @IsOrgAdmin
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void initiateZeroDigital(
      @PathVariable("orgSlug") String orgSlug,
      @RequestBody List<ZeroDigitalDto.ZdRequest> requests) {
    zeroDigitalService.initiateZeroDigital(orgSlug, requests);
  }

  @IsTeacher
  @GetMapping
  public List<ZeroDigitalDto.ZeroDigitalResponse> getZeroDigital(
      @PathVariable(name = "orgSlug") String orgSlug,
      @RequestParam(value = "boardSlug") String boardSlug,
      @RequestParam(value = "gradeSlug") String gradeSlug,
      @RequestParam(value = "sectionUuid", required = false) String sectionUuid,
      @RequestParam(value = "subjectSlug") String subjectSlug) {
    return zeroDigitalService.getZeroDigital(
        orgSlug, boardSlug, gradeSlug, sectionUuid, subjectSlug);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/compliance")
  public List<ZeroDigitalDto.ZeroDigitalReportResponse> getComplianceReport(
      @PathVariable(name = "orgSlug") String orgSlug,
      @RequestParam(value = "boardSlug") String boardSlug,
      @RequestParam(value = "gradeSlug") String gradeSlug,
      @RequestParam(value = "sectionUuid", required = false) String sectionUuid,
      @RequestParam(value = "subjectSlug", required = false) String subjectSlug) {
    return zeroDigitalService.getComplianceReport(
        orgSlug, boardSlug, gradeSlug, sectionUuid, subjectSlug);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/{zdId}")
  public void updateZeroDigital(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("zdId") Long zdId,
      @RequestBody ZeroDigitalDto.ZdUpdateRequest request) {
    zeroDigitalService.updateZeroDigital(orgSlug, zdId, request);
  }

  @IsOrgAdmin
  @PostMapping("/{zdId}")
  public void triggerZeroDigital(
      @PathVariable("orgSlug") String orgSlug, @PathVariable("zdId") Long id) {
    zeroDigitalService.triggerZeroDigital(orgSlug, id);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/{zdId}/chapters/{chapterSlug}")
  public void createZeroDigitalTest(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("zdId") Long zdId,
      @PathVariable("chapterSlug") String chapterSlug) {
    zeroDigitalService.createZeroDigitalTest(orgSlug, zdId, chapterSlug);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/{zdId}/test-schedules/{testScheduleId}")
  public void linkTestToZeroDigital(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("zdId") Long id,
      @PathVariable("testScheduleId") Long testScheduleId) {
    zeroDigitalService.linkTestToZeroDigital(orgSlug, id, testScheduleId);
  }

  @PostMapping("/generate-tests")
  public void generateZeroDigitalTests(@RequestParam(defaultValue = "1000") int limit) {
    zeroDigitalService.generateZeroDigitalTests(limit);
  }
}
