package com.wexl.retail.organization.reports.service;

import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.Subject;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.reports.dto.SubjectsMetaDataDto;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReportsService {

  private final OrganizationRepository organizationRepository;
  private final CurriculumService curriculumService;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final ValidationUtils validationUtils;
  private final StrapiService strapiService;

  public List<SubjectsMetaDataDto.Response> getSubjectsMetaDataByOrg(
      String orgSlug, String board, String grade) {
    Organization org;
    org = organizationRepository.findBySlug(orgSlug);
    var parentOrg = org.getParent();
    List<SubjectsMetaData> subjectsMetaData = null;
    if (grade != null) {
      subjectsMetaData = subjectsMetaDataRepository.findByGradeSlugAndBoardSlug(grade, board);
    }
    if (subjectsMetaData != null) {
      return buildResponseFromSubjectMetaData(subjectsMetaData, grade);
    }
    if (org.getReportsMetadata() != null) {
      return buildResponse(org.getReportsMetadata());
    } else if (parentOrg != null && parentOrg.getReportsMetadata() != null) {
      return buildResponse(parentOrg.getReportsMetadata());
    } else {
      List<EduBoard> boardsHierarchy;
      boardsHierarchy = curriculumService.getBoardsHierarchy(org.getSlug());
      return buildResponseFromBoardsHierarchy(boardsHierarchy, board);
    }
  }

  private List<SubjectsMetaDataDto.Response> buildResponseFromSubjectMetaData(
      List<SubjectsMetaData> subjectsMetaData, String gradeSlug) {
    List<SubjectsMetaDataDto.Response> responseList = new ArrayList<>();
    var gradeEntity = strapiService.getAllGrades();
    var grade = validationUtils.findGradeBySlug(gradeEntity, gradeSlug);
    responseList.add(
        SubjectsMetaDataDto.Response.builder()
            .gradeName(grade.getName())
            .gradeSlug(grade.getSlug())
            .subjectResponses(buildSubjectsMetaData(subjectsMetaData))
            .build());
    return responseList;
  }

  private List<SubjectsMetaDataDto.SubjectResponse> buildSubjectsMetaData(
      List<SubjectsMetaData> subjectsMetaData) {
    return subjectsMetaData.stream()
        .map(
            subject ->
                SubjectsMetaDataDto.SubjectResponse.builder()
                    .name(subject.getName())
                    .subjectSlug(subject.getWexlSubjectSlug())
                    .build())
        .toList();
  }

  private List<SubjectsMetaDataDto.Response> buildResponseFromBoardsHierarchy(
      List<EduBoard> boardsHierarchy, String boardSlug) {
    List<SubjectsMetaDataDto.Response> responseList = new ArrayList<>();
    boardsHierarchy.stream()
        .filter(board -> board.getSlug().equals(boardSlug))
        .findFirst()
        .ifPresent(
            board ->
                board
                    .getGrades()
                    .forEach(grade -> responseList.add(buildHierarchyGradeResponse(grade))));
    return responseList;
  }

  private SubjectsMetaDataDto.Response buildHierarchyGradeResponse(Grade grade) {
    return SubjectsMetaDataDto.Response.builder()
        .gradeName(grade.getName())
        .gradeSlug(grade.getSlug())
        .subjectResponses(buildHierarchySubjectResponseData(grade.getSubjects()))
        .build();
  }

  private List<SubjectsMetaDataDto.SubjectResponse> buildHierarchySubjectResponseData(
      List<Subject> subjects) {
    return subjects.stream()
        .map(
            subject ->
                SubjectsMetaDataDto.SubjectResponse.builder()
                    .name(subject.getName())
                    .subjectSlug(subject.getSlug())
                    .build())
        .toList();
  }

  private SubjectsMetaDataDto.Response buildGradeResponse(SubjectsMetaDataDto.Grades grade) {
    return SubjectsMetaDataDto.Response.builder()
        .gradeName(grade.name())
        .gradeSlug(grade.gradeSlug())
        .subjectResponses(buildSubjectResponseData(grade.subjects()))
        .build();
  }

  private List<SubjectsMetaDataDto.SubjectResponse> buildSubjectResponseData(
      List<SubjectsMetaDataDto.Subjects> subjects) {
    return subjects.stream()
        .map(
            subject ->
                SubjectsMetaDataDto.SubjectResponse.builder()
                    .name(subject.displayName())
                    .subjectSlug(buildSubjectSlug(subject))
                    .build())
        .toList();
  }

  private String buildSubjectSlug(SubjectsMetaDataDto.Subjects subject) {
    if (subject.wexlSubjectSlug() != null) {
      return subject.wexlSubjectSlug();
    }
    String[] words = subject.displayName().split(" ");
    return String.join("_", words).toLowerCase();
  }

  private List<SubjectsMetaDataDto.Response> buildResponse(
      SubjectsMetaDataDto.GradesData reportsMetadata) {
    return reportsMetadata.grades().stream().map(this::buildGradeResponse).toList();
  }
}
