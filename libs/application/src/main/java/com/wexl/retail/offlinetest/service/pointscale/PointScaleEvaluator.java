package com.wexl.retail.offlinetest.service.pointscale;

import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class PointScaleEvaluator {

  private final FourPointScale fourPointScale;
  private final EightPointScale eightPointScale;
  private final ThreePointScale threePointScale;
  private final DefaultPointScale defaultPointScale;
  private final FivePointScale fivePointScale;
  private final NinePointScale ninePointScale;
  private final SixPointScale sixPointScale;
  private final DpsFivePointScale dpsFivePointScale;
  private final SaiSeniorPointScale saiSeniorPointScale;
  private final DpsBalavatikaPointScale dpsBalavatikaPointScale;
  private final DpsBalavatikaThreePointScale dpsBalavatikaThreePointScale;
  private final DpsBalavatikaPointScaleForTerm2 dpsBalavatikaPointScaleForTerm2;

  public PointScale getPointScale(String pointScale) {
    return switch (pointScale) {
      case "4point" -> fourPointScale;
      case "8point" -> eightPointScale;
      case "3point" -> threePointScale;
      case "5point" -> fivePointScale;
      case "9point" -> ninePointScale;
      case "6point" -> sixPointScale;
      case "saiSeniorPointScale" -> saiSeniorPointScale;
      case "5point-scholastic" -> dpsFivePointScale;
      case "6point_pre_primary" -> dpsBalavatikaPointScale;
      case "3point_pre_primary" -> dpsBalavatikaThreePointScale;
      case "5point_pre_primary" -> dpsBalavatikaPointScaleForTerm2;
      default -> defaultPointScale;
    };
  }

  public String evaluate(String pointScale, BigDecimal marks) {
    return getPointScale(pointScale).evaluate(marks);
  }

  public BigDecimal marks(String pointScale, String grade) {
    return getPointScale(pointScale).getMarks(grade);
  }

  public List<String> getGradesByPointScale(String pointScale) {
    return getPointScale(pointScale).getGradesByPointScale(pointScale);
  }
}
