package com.wexl.retail.reportcards.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.*;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "report_card_config")
public class ReportCardConfig extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String title;

  @JsonIgnore
  @OneToMany(
      fetch = FetchType.LAZY,
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      mappedBy = "reportCard")
  private List<ReportCardConfigDetail> reportCardConfigDetails;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "grade_name")
  private String gradeName;

  @Column(name = "board_name")
  private String boardName;

  @Column(name = "org_slug")
  private String orgSlug;

  private Boolean studentViewEnabled;

  @Column(name = "template_id")
  private Long templateId;

  @Column(name = "subject_metadata_id")
  private Long subjectMetadataId;

  @Column(name = "with_marks")
  private Boolean withMarks;
}
