package com.wexl.retail.reportcards.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

public record ReportCardConfigDto() {
  @Builder
  public record ReportCardRequest(
      @JsonProperty("title") String title,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("report_card_template_id") Long reportCardTemplateId,
      @JsonProperty("student_view_enabled") Boolean studentViewEnabled,
      @JsonProperty("term_details") List<TermRequest> termRequest,
      @JsonProperty("with_marks") Boolean withMarks,
      @JsonProperty("subject_metadata_id") Long subjectMetadataId) {}

  @Builder
  public record TermRequest(
      @JsonProperty("assessment_id") Long assessmentId,
      @JsonProperty("weightage") Long weightage,
      @JsonProperty("seq_no") Long seqNo) {}

  @Builder
  public record ReportCardJobDetails(
      Long id,
      @JsonProperty("last_processed_time") Long lastProcessedTime,
      @JsonProperty("config_id") Long configId,
      @JsonProperty("report_card_configs")
          List<ReportCardConfigResponse> reportCardConfigResponse) {}

  @Builder
  public record ReportCardConfigResponse(
      Long id,
      @JsonProperty("title") String title,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("with_marks") Boolean withMarks,
      @JsonProperty("job_id") Long reportCardJobId,
      @JsonProperty("job_status") ReportCardJobStatus jobStatus,
      @JsonProperty("last_processed_at") Long lastProcessedAt,
      @JsonProperty("failure_reason") String failureReason,
      @JsonProperty("student_view_enabled") Boolean studentViewEnabled,
      @JsonProperty("report_card_template_id") Long reportCardTemplateId,
      @JsonProperty("subject_metadata_id") Long subjectMetadataId,
      @JsonProperty("report_card_template_type") String reportCardTemplateType,
      @JsonProperty("report_card_config_details")
          List<ReportCardConfigDetails> reportCardConfigDetails) {}

  @Builder
  public record ReportCardConfigDetails(
      Long id,
      @JsonProperty("term_id") Long termId,
      @JsonProperty("term_name") String termName,
      @JsonProperty("term_assessment_name") String assessmentName,
      @JsonProperty("term_assessment_id") Long assessmentId,
      @JsonProperty("weightage") Long weightage,
      @JsonProperty("seq_no") Long seqNo) {}

  @Builder
  public record StudentReportCardResponse(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("admission_number") String admissionNumber,
      @JsonProperty("class_roll_number") String classRollNumber,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("class_section") String gradeAndSection,
      @JsonProperty("father_name") String fatherName,
      @JsonProperty("academic_year") String academicYear,
      @JsonProperty("term_details") List<TermDetails> termDetails,
      @JsonProperty("over_all_percentage") List<FinalPercentage> overAllPercentage,
      @JsonProperty("final_marks_grade") FinalMarksGrade finalMarksGrade) {}

  @Builder
  public record FinalMarksGrade(
      @JsonProperty("final_marks") BigDecimal finalMarks,
      @JsonProperty("final_grade") String finalGrade) {}

  @Builder
  public record TermDetails(
      @JsonProperty("term_id") Long id,
      @JsonProperty("term_name") String title,
      @JsonProperty("test_schedule_details") List<TestScheduleDetails> testSchedules,
      @JsonProperty("over_all_percentage") OverAllPercentage overAllPercentage) {}

  @Builder
  public record TestScheduleDetails(
      @JsonProperty("test_schedule_id") Long testScheduleId,
      @JsonProperty("test_schedule_name") String testScheduleName,
      @JsonProperty("test_definition_details") List<TestDefinitionDetails> examDetails,
      @JsonProperty("over_all_percentage") OverAllPercentage overAllPercentage) {}

  @Builder
  public record TestDefinitionDetails(
      @JsonProperty("test_definition_id") Long testDefId,
      @JsonProperty("test_definition_name") String testDefName,
      @JsonProperty("student_marks") BigDecimal studentMarks,
      @JsonProperty("grade") String grade) {}

  @Builder
  public record OverAllPercentage(
      @JsonProperty("over_all_marks") String overAllMarks,
      @JsonProperty("student_total_marks") BigDecimal studentTotalMarks,
      @JsonProperty("grade") String grade) {}

  @Builder
  public record FinalPercentage(
      @JsonProperty("terms_total") BigDecimal termsTotalMarks,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("student_total_marks") BigDecimal studentTotalMarks,
      @JsonProperty("grade") String grade) {}

  @Getter
  @RequiredArgsConstructor
  public enum ReportCardJobStatus {
    NOT_STARTED("NOT_STARTED"),
    IN_PROGRESS("IN_PROGRESS"),
    FAILED("FAILED"),
    COMPLETED("COMPLETED"),
    UPLOADING("UPLOADING"),
    SAVED_REPORTS_SUCCESS("SAVED_REPORTS"),
    SAVED_REPORTS_FAILED("SAVED_REPORTS_FAILED");

    private final String value;

    public static ReportCardJobStatus fromValue(String value) {
      for (ReportCardJobStatus type : ReportCardJobStatus.values()) {
        if (type.value.equals(value)) {
          return type;
        }
      }
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.BuildErrors", new String[] {value});
    }
  }

  public record ReportCardJobRequest(@NotNull Long reportCardConfigId) {}

  @Builder
  public record ReportCardJobResponse(@JsonProperty("ref_id") Long refId) {}

  @Builder
  public record ReportCardSectionWiseResponse(
      @JsonProperty("academic_year") String academicYear,
      @JsonProperty("campus") String campus,
      @JsonProperty("curriculum") String curriculum,
      @JsonProperty("class_grade") String classGrade,
      @JsonProperty("exam_type") String examType,
      @JsonProperty("subject") String subject,
      @JsonProperty("sections") List<SectionDetail> sections) {}

  @Builder
  public record SectionDetail(
      @JsonProperty("section") String section,
      @JsonProperty("teacher_name") String teacherName,
      @JsonProperty("absents") Long absents,
      @JsonProperty("total_students") int totalStudents,
      @JsonProperty("grade_details") List<GradeDetail> gradeDetails) {}

  @Builder
  public record GradeDetail(
      @JsonProperty("grade") String grade, @JsonProperty("count") int count) {}

  @Builder
  public record MergeAssessmentCategoryRequest(
      @JsonProperty("category_ids") List<Long> categoryIds) {}

  @Builder
  public record Body(
      String name,
      String gradeFacilitatorName,
      String grade,
      String orgSlug,
      String gradeSlug,
      String termSlug,
      String schoolSectionName,
      String assessment,
      String attendance,
      FirstPageFirstTable firstTable,
      SecondPage secondPage,
      AoReport aoReport) {}

  @Builder
  public record AoReport(List<AoTable> aoTables) {}

  @Builder
  public record AoTable(
      String title,
      String subjectSlug,
      List<AoDetail> aoDetails,
      String aofValue,
      String aosValue,
      String aorValue) {}

  @Builder
  public record AoDetail(String learningStatement, String indicator) {}

  @Builder
  public record SecondPage(FirstTable firstTable, SecondTable secondTable, ThirdTable thirdTable) {}

  @Builder
  public record FirstTable(
      String firstTableTitle,
      Table firstTableAreas,
      Table firstTableLearningLevel,
      Table firstTableGrade) {}

  @Builder
  public record SecondTable(
      String secondTableTitle,
      Table secondTableAreas,
      Table secondTableLearningLevel,
      Table secondTableGrade) {}

  @Builder
  public record ThirdTable(
      String thirdTableTitle,
      Table thirdTableAreas,
      Table thirdTableLearningLevel,
      Table thirdTableGrade) {}

  @Builder
  public record Table(
      String column1, String column2, String column3, String column4, String column5) {}

  @Builder
  public record FirstPageFirstTable(
      String title,
      String listeningGrade,
      String listeningValue,
      String speakingGrade,
      String speakingValue,
      String readingGrade,
      String readingValue,
      String writingGrade,
      String writingValue,
      String psewGrade,
      String psewValue,
      String phyDevGrade,
      String phyDevValue,
      String mathGrade,
      String mathValue,
      String untwGrade,
      String untwValue,
      String teluguOrHindiGrade,
      String teluguOrHindiValue,
      String comment) {}

  @Builder
  public record GradeAndPercentage(
      @JsonProperty("grade") String grade,
      @JsonProperty("percentage") String percentage,
      @JsonProperty("student") Student student) {}

  @Builder
  public record GradeAndPercentageResponse(
      @JsonProperty("grade") String grade,
      @JsonProperty("percentage") String percentage,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("section_name") String sectionName) {}
}
