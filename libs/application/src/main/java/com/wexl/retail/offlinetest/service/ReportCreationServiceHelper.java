package com.wexl.retail.offlinetest.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Service
public class ReportCreationServiceHelper {

  private final OfflineTestReportService offlineTestReportService;

  private final SpringTemplateEngine stringBasedSpringTemplateEngine;

  @Autowired
  public ReportCreationServiceHelper(
      OfflineTestReportService offlineTestReportService,
      @Qualifier("stringBasedSpringTemplateEngine")
          SpringTemplateEngine stringBasedSpringTemplateEngine) {
    this.offlineTestReportService = offlineTestReportService;
    this.stringBasedSpringTemplateEngine = stringBasedSpringTemplateEngine;
  }

  public byte[] uploadAndProcess(byte[] templateBytes, byte[] dataBytes) throws IOException {
    final String template =
        IOUtils.toString(new ByteArrayInputStream(templateBytes), StandardCharsets.UTF_8);
    final String json =
        IOUtils.toString(new ByteArrayInputStream(dataBytes), StandardCharsets.UTF_8);
    ObjectMapper mapper = new ObjectMapper();
    Map<String, Object> map = mapper.readValue(json, new TypeReference<>() {});
    final String resolvedFoText = getTemplateFromMap(template, map);
    return offlineTestReportService.generatePdf(resolvedFoText);
  }

  public String getTemplateFromMap(String htmlContent, Map<String, Object> dynamicAttibutesMap) {
    final Context ctx = new Context(Locale.of("en"));
    dynamicAttibutesMap.forEach((k, v) -> ctx.setVariable(k, v));
    return stringBasedSpringTemplateEngine.process(htmlContent, ctx);
  }
}
