package com.wexl.retail.meetingroom.service;

import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.model.ClassroomSchedule;
import com.wexl.retail.classroom.core.repository.ClassroomScheduleRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.meetingroom.domain.MeetingRoom;
import com.wexl.retail.meetingroom.dto.MeetingRoomRequest;
import com.wexl.retail.meetingroom.dto.MeetingRoomResponse;
import com.wexl.retail.meetingroom.repository.MeetingRoomRepository;
import com.wexl.retail.util.Constants;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MeetingRoomService {
  private final MeetingRoomRepository meetingRoomRepository;
  private final ClassroomScheduleRepository classroomScheduleRepository;

  public List<MeetingRoomResponse> getAllMeetingResponses(String orgSlug) {
    List<MeetingRoom> meetingRoomsByOrganisation =
        meetingRoomRepository.findByOrgSlugAndDeletedAtIsNull(orgSlug);

    return meetingRoomsByOrganisation.stream()
        .map(
            meetingRoom ->
                MeetingRoomResponse.builder()
                    .id(meetingRoom.getId())
                    .hostLink(meetingRoom.getHostLink())
                    .joinLink(meetingRoom.getJoinLink())
                    .name(meetingRoom.getName())
                    .displayName(meetingRoom.getDisplayName())
                    .type(meetingRoom.getType())
                    .createdDate(
                        DateTimeUtil.convertIso8601ToEpoch(
                            meetingRoom.getCreatedAt().toLocalDateTime()))
                    .build())
        .sorted(Comparator.comparing(MeetingRoomResponse::getCreatedDate).reversed())
        .toList();
  }

  public void createRequest(MeetingRoomRequest meetingRoomRequest, String orgSlug) {

    var meetingRoom =
        meetingRoomRepository.findByNameAndOrgSlug(meetingRoomRequest.getName(), orgSlug);

    if (meetingRoom.isPresent()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.MeetingName.exists",
          new String[] {meetingRoomRequest.getName()});
    }
    var meetingRoomsByLinkAndOrgSlug =
        meetingRoomRepository.findAllMeetingRoomsByJoinLinkAndOrgSlug(
            meetingRoomRequest.getJoinLink(), orgSlug);
    if (!meetingRoomsByLinkAndOrgSlug.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.MeetingLink.exists",
          new String[] {meetingRoomRequest.getLink()});
    }
    MeetingRoom meetingroom = new MeetingRoom();
    meetingroom.setName(meetingRoomRequest.getName());
    meetingroom.setType(meetingRoomRequest.getType());
    meetingroom.setHostLink(meetingRoomRequest.getHostLink());
    meetingroom.setJoinLink(meetingRoomRequest.getJoinLink());
    meetingroom.setOrgSlug(orgSlug);
    meetingroom.setDisplayName(meetingRoomRequest.getDisplayName());
    meetingRoomRepository.save(meetingroom);
  }

  public void updateRequest(MeetingRoomRequest meetingRoomRequest, String orgSlug, Long id) {

    var meetingRoomsByIdAndOrgSlug =
        meetingRoomRepository.findAllMeetingRoomsByIdAndOrgSlug(id, orgSlug);

    if (meetingRoomsByIdAndOrgSlug.size() != 1) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.MeetingRoom.Update",
          new String[] {Long.toString(id)});
    }
    MeetingRoom meetingRoom = meetingRoomsByIdAndOrgSlug.getFirst();
    meetingRoom.setName(meetingRoomRequest.getName());
    meetingRoom.setType(meetingRoomRequest.getType());
    meetingRoom.setHostLink(meetingRoomRequest.getHostLink());
    meetingRoom.setJoinLink(meetingRoomRequest.getJoinLink());
    meetingRoom.setDisplayName(meetingRoomRequest.getDisplayName());
    meetingRoomRepository.save(meetingRoom);
  }

  public void deleteRequest(String orgSlug, Long id) {
    var meetingRoomsByIdAndOrgSlug =
        meetingRoomRepository.findAllMeetingRoomsByIdAndOrgSlug(id, orgSlug);
    if (meetingRoomsByIdAndOrgSlug.size() != 1) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.MeetingRoom.Delete",
          new String[] {Long.toString(id)});
    }

    MeetingRoom meetingRoom = meetingRoomsByIdAndOrgSlug.getFirst();
    List<ClassroomSchedule> classRoomSchedules =
        classroomScheduleRepository.findByOrgSlugAndMeetingRoom(orgSlug, meetingRoom);
    if (!classRoomSchedules.isEmpty()
        && Objects.isNull(classRoomSchedules.getFirst().getClassroom().getDeletedAt())) {
      var classroomNames =
          classRoomSchedules.stream()
              .map(ClassroomSchedule::getClassroom)
              .map(Classroom::getName)
              .distinct()
              .toList();
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassRoom.Delete",
          new String[] {(classroomNames).toString()});
    }
    final var timestamp = Timestamp.valueOf(LocalDateTime.now());
    final var date = new Date(timestamp.getTime());
    meetingRoom.setDeletedAt(date);
    meetingRoomRepository.save(meetingRoom);
  }

  public MeetingRoomResponse getMeetingRoomResponses(Long meetingRoomId, String orgSlug) {
    var meetingRooms =
        meetingRoomRepository.findByIdAndOrgSlugIn(
            meetingRoomId, List.of(orgSlug, Constants.WEXL_INTERNAL));

    if (meetingRooms.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MeetingRoomEmpty");
    }
    MeetingRoom firstMeetingRoom = meetingRooms.get();
    return MeetingRoomResponse.builder()
        .id(firstMeetingRoom.getId())
        .hostLink(firstMeetingRoom.getHostLink())
        .joinLink(firstMeetingRoom.getJoinLink())
        .name(firstMeetingRoom.getName())
        .type(firstMeetingRoom.getType())
        .createdDate(
            DateTimeUtil.convertIso8601ToEpoch(firstMeetingRoom.getCreatedAt().toLocalDateTime()))
        .build();
  }
}
