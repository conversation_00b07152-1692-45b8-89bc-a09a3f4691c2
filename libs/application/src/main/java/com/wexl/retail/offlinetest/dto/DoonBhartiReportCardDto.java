package com.wexl.retail.offlinetest.dto;

import java.util.List;
import lombok.Builder;

public record DoonBhartiReportCardDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String sessionStart,
      String sessionEnd,
      String orgSlug,
      String className,
      String sectionName,
      String studentName,
      String fatherName,
      Long rollNo,
      String gradeSlug,
      String name,
      String dob,
      String admissionNumber,
      String address,
      String phoneNumber) {}

  @Builder
  public record Body(
      FirstTable firstTable,
      SecondTable secondTable,
      boolean isParticipatedInInternal,
      boolean isParticipated,
      Long totalWorkingDays,
      Long totalPresentDays,
      String height,
      String weight,
      String overallRemark,
      boolean promotedToNextClass) {}

  @Builder
  public record FirstTable(List<Marks> marks) {}

  @Builder
  public record SecondTable(List<SecMarks> marks) {}

  @Builder
  public record Marks(
      String subjectName,
      String unitTest1,
      String portFolio,
      String cognitiveDevelopment,
      String ma,
      String halfYearly,
      Double total,
      String grade) {}

  @Builder
  public record SecMarks(String subjectName, String grade) {}

  @Builder
  public record TableMarks(List<Marks> firstTableMarks, List<SecMarks> secondTableMarks) {}
}
