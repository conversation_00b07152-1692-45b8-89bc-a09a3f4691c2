package com.wexl.retail.offlinetest.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.model.TermAssessmentCategory;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "offline_test_definition")
public class OfflineTestDefinition extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "academic_year_slug")
  private String academicYearSlug;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "board_name")
  private String boardName;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "grade_name")
  private String gradeName;

  @Column(name = "section_uuid")
  private String sectionUuid;

  @Column(name = "exam_start_date")
  private LocalDateTime examStartDate;

  @Column(name = "exam_end_date")
  private LocalDateTime examEndDate;

  @Column(name = "teacher_id")
  private Long teacherId;

  @Column(name = "title")
  private String title;

  @Column(name = "org_slug")
  private String orgSlug;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "offlineTestDefinition", fetch = FetchType.LAZY)
  private List<OfflineTestSchedule> offlineTestScheduleSchedule;

  @Column(name = "created_by")
  private String createdBy;

  private Boolean notificationStatus;

  @Column(name = "term_slug")
  private String termSlug;

  @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  private TermAssessment assessment;

  @ManyToOne(fetch = FetchType.LAZY)
  private TermAssessmentCategory assessmentCategory;

  @Column(name = "grade_scale_slug")
  private String gradeScaleSlug;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "offlineTestDefinition", fetch = FetchType.LAZY)
  private List<OfflineTestScheduleStudentAttendance> offlineTestScheduleStudentAttendanceAndRemarks;

  @Column(name = "total_attendance_days")
  private String totalAttendanceDays;

  @Column(name = "report_card_path")
  private String generatedReportCardPath;

  @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private ReportCardTemplate reportCardTemplate;

  private Boolean showStudents;
  private Boolean showAdmitCard;
}
