package com.wexl.retail.offlinetest.repository;

import java.util.Date;

public interface LowerGradeReportCardData {
  String getSubjectName();

  Long getTotal();

  Double getMarks();

  Long getMarks2();

  Long getMarks3();

  String getTitle();

  String getIsAttended();

  Long getSeqNo();

  String getType();

  String getCategory();

  String getAssessmentCategory();

  String getTermSlug();

  Double getTotalMarks();

  Long getSubjectMarks();

  Long getOtdId();

  String getAssessmentSlug();

  String getRemarks();

  Long getStudentId();

  Date getStartDate();

  String getTestName();

  String getSubjectSlug();

  Double getActualMarks();

  Boolean getConsiderPercentage();
}
