package com.wexl.retail.offlinetest.service;

import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.dto.response.TeacherCurriculumResponse;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.util.StrapiService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(100)
@Component
@RequiredArgsConstructor
public class DefaultClassTeacherSubjectHandler implements ClassTeacherSubjectHandler {

  private final SubjectsMetaDataRepository subjectsMetaDataRepository;

  private final CurriculumService curriculumService;

  private final StrapiService strapiService;

  public List<SubjectsMetaData> getClassTeacherPreferredSubjectSlugs(Section section) {
    return subjectsMetaDataRepository.findByOrgSlugAndGradeSlugAndBoardSlug(
        section.getOrganization(), section.getGradeSlug(), section.getBoardSlug());
  }

  public List<TeacherCurriculumResponse> getClassTeacherSubjects(Section section) {
    var board = strapiService.getEduBoardBySlug(section.getBoardSlug());
    var subjects =
        curriculumService.getSubjectsByBoardIdAndGradeId(
            section.getOrganization(), board.getId(), section.getGradeId());
    return subjects.stream()
        .map(
            subject ->
                TeacherCurriculumResponse.builder()
                    .boardSlug(section.getBoardSlug())
                    .gradeId(section.getGradeId())
                    .subjectSlug(subject.getSlug())
                    .sectionUuid(section.getUuid())
                    .build())
        .toList();
  }
}
