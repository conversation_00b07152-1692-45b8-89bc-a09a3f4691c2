package com.wexl.retail.communications.homework.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.notifications.dto.NotificationDto;
import java.util.List;
import lombok.Builder;

public record HomeworkDto() {

  public record HomeworkRequest(
      String title,
      String message,
      @JsonProperty("student_ids") List<Long> studentIds,
      @JsonProperty("section_uuids") List<String> sectionUuids,
      List<String> attachment,
      List<String> link) {}

  @Builder
  public record HomeworkResponse(
      List<NotificationDto.TeacherNotificationResponse> homeworkResponse) {}

  @Builder
  public record StudentHomeworkResponse(
      List<NotificationDto.StudentNotificationResponse> homeworkResponse) {}

  @Builder
  public record HomeWorkDetails(
      String teacherName,
      Long date,
      String title,
      String message,
      String section,
      String attachments) {}
}
