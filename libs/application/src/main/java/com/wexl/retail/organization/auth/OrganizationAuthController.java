package com.wexl.retail.organization.auth;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.dto.MobileNumberLoginDto;
import com.wexl.retail.generic.GenericResponse;
import com.wexl.retail.model.OtpVerificationRequest;
import com.wexl.retail.organization.dto.OrganizationResponse;
import com.wexl.retail.otp.OtpRequest;
import com.wexl.retail.otp.OtpResponse;
import com.wexl.retail.otp.OtpService;
import com.wexl.retail.util.Constants;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/auth")
public class OrganizationAuthController {

  @Autowired AuthService authService;
  @Autowired OrganizationAuthService organizationAuthService;

  @Autowired private OtpService otpService;

  @PostMapping("/user/email:validate")
  public OtpResponse sendEmailOtp(@RequestBody OtpRequest otpRequest) {

    return organizationAuthService.sendOtp(
        otpRequest.getCaptchaCode(),
        otpRequest.getTarget(),
        Constants.EMAIL_VERIFICATION_REFERENCE);
  }

  @PostMapping("/user/phone:validate")
  public OtpResponse sendMobileOtp(@RequestBody OtpRequest otpRequest) {

    return otpService.sendOtpByMsg91(otpRequest.getTarget());
  }

  @PostMapping("/user/email:verify")
  public GenericResponse verifyEmailOtp(
      @Valid @RequestBody OtpVerificationRequest verificationRequest) {

    return organizationAuthService.verifyOtp(verificationRequest);
  }

  @PostMapping("/user/phone:verify")
  public MobileNumberLoginDto.MobileNumberLoginResponse verifyMobileOtp(
      @Valid @RequestBody OtpVerificationRequest verificationRequest) {

    var mobileRequest =
        MobileNumberLoginDto.MobileNumberLoginRequest.builder()
            .mobileNumber(verificationRequest.getMobileNumber())
            .otp(Long.valueOf(verificationRequest.getOtp()))
            .build();

    return otpService.verifyOtpByMsg91(mobileRequest, null);
  }

  @GetMapping("/orgs/info")
  public List<OrganizationResponse> getAllOrgCreateByWexlInternal() {
    return organizationAuthService.getAllOrgs();
  }
}
