package com.wexl.retail.offlinetest.repository;

import com.wexl.retail.metrics.reportcards.dto.SubjectWiseResponse;
import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.offlinetest.model.OfflineTestSchedule;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface OfflineTestScheduleRepository extends JpaRepository<OfflineTestSchedule, Long> {

  @Query(
      value =
          """
                          select ots.* from offline_test_schedule ots  join offline_test_definition otd
                          on otd.id = ots.offline_test_definition_id
                                  where otd.org_slug in (:orgSlug)
                                  and (cast((:examType) as varChar) is null or otd.id in (:examType))
                                  and (cast((:subjectSlug) as varChar) is null or ots.subject_slug in (:subjectSlug))
                                  and (cast((:gradeList) as varChar) is null or otd.grade_slug in (:gradeList))
                                  and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                                  and ots.published_at is not null""",
      nativeQuery = true)
  List<OfflineTestSchedule> getOfflineTestScheduleByOrgSlug(
      String orgSlug,
      List<String> gradeList,
      List<String> sectionUuid,
      List<String> examType,
      List<String> subjectSlug);

  @Query(
      value =
          """

                          select concat(u.first_name, ' ', u.last_name) as fullName,s.id as studentId, ts.org_slug as orgSlug,o.name as orgName,
                          se.grade_slug as gradeSlug
                          ,se.grade_name as gradeName,td.test_name as testName,
                          td.id as testDefinitionId,td.subject_slug as subjectSlug,td.total_marks as totalMarks,tss.id as testScheduleStudentId,
                          e.marks_scored as marksScored,e.id as examId,se.name as sectionName,ts.start_date as scheduleDate
                          from test_schedule ts join test_definitions td on td.id = ts.test_definition_id
                          join test_schedule_student tss on tss.schedule_test_id =  ts.id
                          join users u on u.id = tss.student_id
                          join students s on s.user_id = u.id
                          join sections se on se.id =  s.section_id
                          join orgs o on o.slug = ts.org_slug
                          left join exams e on  e.schedule_test_id = ts.id and e.student_id  = s.id
                          where o.slug in (:orgSlug) and date(ts.start_date) between :startDate and :endDate
                          and (cast((:gradeSlug) as varChar) is null or se.grade_slug in (:gradeSlug))
                          order by scheduleDate ,ts.org_slug, gradeSlug, sectionName asc
                                       """,
      nativeQuery = true)
  List<AllStudentsExamDataByOrg> getAllStudentsExamDataByOrg(
      List<String> orgSlug, LocalDateTime startDate, LocalDateTime endDate, List<String> gradeSlug);

  @Query(
      value =
          """
                   SELECT
                         otd.title,
                         sm.id,
                         otd.grade_name as gradeName,
                         otd.section_uuid as sectionName,
                         sm.name as subjectName,
                         COUNT(case when s.deleted_at is null then 1 end) as totalCount,
                         COUNT(CASE WHEN otss.offline_test_schedule_id IS NOT NULL and s.deleted_at is null AND otss.marks IS NOT NULL THEN 1 END) as marksEnteredCount,
                         COUNT(CASE WHEN otss.offline_test_schedule_id IS NOT NULL and s.deleted_at is null AND otss.marks IS NULL THEN 0 END) as marksNotEnteredCount,
                         count(distinct otss.student_id) filter (where otss.is_attended=false) as absenteesCount
                         FROM
                     offline_test_definition otd
                  JOIN
                     offline_test_schedule ots ON otd.id = ots.offline_test_definition_id
                  JOIN
                     subject_metadata sm ON sm.id = ots.subject_metadata_id
                  LEFT JOIN
                     offline_test_schedule_student otss ON otss.offline_test_schedule_id = ots.id
                  Join
                    students s on s.id = otss.student_id
                  WHERE
                     otd.org_slug = :org
                     AND otd.board_slug = :boardSlug
                     AND otd.grade_slug = :gradeSlug
                     AND otd.section_uuid = CAST(:sectionUuid AS varchar)
                     AND otd.id = :scheduleId
                     AND ots.deleted_at is null
                  GROUP BY
                     otd.title,
                     otd.grade_name,
                     otd.section_uuid,
                     sm.name,
                     sm.id,
                     ots.id
                     """,
      nativeQuery = true)
  List<SubjectWiseResponse> getSubjectsByOrgAndBoardAndGradeAndSectionAndSchedule(
      String org, String boardSlug, String gradeSlug, String sectionUuid, Integer scheduleId);

  @Query(
      value =
          """
                  select * from offline_test_schedule ots where subject_slug =:subjectSlug and offline_test_definition_id = :offlineTestDefinitionId
                  and subject_name = :subjectName and deleted_at is not null
                  """,
      nativeQuery = true)
  OfflineTestSchedule findBySubjectAndOfflineTestDefinitionId(
      String subjectSlug, Long offlineTestDefinitionId, String subjectName);

  @Query(
      value =
          """

                          SELECT otss.student_id as studentId, coalesce (SUM(otss.marks),0) AS marks, CONCAT(u.first_name, ' ', u.last_name) AS name,u.auth_user_id as authUserId
                          FROM offline_test_schedule_student otss
                          JOIN students s ON s.id = otss.student_id
                          JOIN users u ON u.id = s.user_id
                          WHERE otss.offline_test_schedule_id IN (:offlineTestScheduleIds)
                          GROUP BY otss.student_id, u.first_name, u.last_name,u.auth_user_id
                          """,
      nativeQuery = true)
  List<ReportCardStudentData> getStudentMarksByOfflineTestScheduleId(
      List<Long> offlineTestScheduleIds);

  @Query(
      value =
          """
                          select ots.* from offline_test_schedule ots join offline_test_definition otd on otd.id = ots.offline_test_definition_id\s
                          where ots.subject_metadata_id  in (:metadataIds)
                          and otd.section_uuid  =:uuid
                                  """,
      nativeQuery = true)
  List<OfflineTestSchedule> getOfflineTestSchedulesBySubjectMetadataAndSection(
      List<Long> metadataIds, String uuid);

  @Query(
      value =
          """
      select distinct rcc.student_id as studentId, sm."name" AS subjectName ,COALESCE(sum(rcc.calculated_marks) ,0) AS marks,
               case when  sum(rccd.weightage) is null then  sum(ots.marks ) else  sum(rccd.weightage) end  as totalMarks
               from report_card_config_data rcc
               join report_card_config_details rccd on  rcc.report_card_config_detail_id  = rccd.id
               join offline_test_schedule_student otss ON otss.id = rcc.otss_id
               join offline_test_schedule ots on ots.id  = otss.offline_test_schedule_id
               join term_assessments ta on ta.id = rccd.term_assessment_id
               join subject_metadata_students sms on sms.student_id = rcc.student_id
               join subject_metadata sm on sm.id = sms.subject_metadata_id and ots.subject_metadata_id  = sm.id
                 where rcc.student_id in  (:studentIds)  and rccd.term_assessment_id in (:assessmentIds)
                  and sm.category = 'SCHOLASTIC' and sm."type" = 'MANDATORY'
                 group  by subjectName,rcc.student_id""",
      nativeQuery = true)
  List<LowerGradeReportCardData> getStudentReportByStudents(
      List<Long> studentIds, List<Long> assessmentIds);

  List<OfflineTestSchedule> findByOfflineTestDefinitionIn(
      List<OfflineTestDefinition> testDefinition);

  @Query(
      value =
          """
                  select ots.* from offline_test_schedule ots
                  join subject_metadata sm on sm.id = ots.subject_metadata_id
                  where offline_test_definition_id  = :offlineTestDefinitionId
                  """,
      nativeQuery = true)
  List<OfflineTestSchedule> findByTestDefinitionId(Long offlineTestDefinitionId);
}
