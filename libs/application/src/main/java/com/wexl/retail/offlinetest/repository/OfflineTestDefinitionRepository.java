package com.wexl.retail.offlinetest.repository;

import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.model.TermAssessmentCategory;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface OfflineTestDefinitionRepository
    extends JpaRepository<OfflineTestDefinition, Long> {

  @Query(
      value =
          """
                        select distinct(otd.*) from offline_test_definition otd
                        join offline_test_schedule ots on otd.id = ots.offline_test_definition_id
                          join sections s on  CAST(s."uuid" AS VARCHAR) = otd.section_uuid
                          where otd.teacher_id in (:teacherId)
                          and (cast((:boardSlug) as varChar) is null or otd.board_slug in (:boardSlug))
                          and (cast((:gradeSlug) as varChar) is null or otd.grade_slug in (:gradeSlug))
                          and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                          and (cast((:academicYearSlug) as varChar ) is null or otd.academic_year_slug in (:academicYearSlug))
                          and s.deleted_at is null
                          order by created_at desc""",
      nativeQuery = true)
  List<OfflineTestDefinition> getOfflineTestScheduleByTeacherId(
      List<Long> teacherId,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      String academicYearSlug);

  @Query(
      value =
          """
          select distinct(otd.*) from offline_test_definition otd join offline_test_schedule ots on otd.id = ots.offline_test_definition_id
          where  (cast((:gradeSlug) as varChar) is null or otd.grade_slug in (:gradeSlug))
                          and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                          and (cast(:examType as bigint) is null or otd.id in (cast(:examType as bigint)))
                          and otd.org_slug = :orgSlug
          """,
      nativeQuery = true)
  List<OfflineTestDefinition> getOfflineTestScheduleByOrgSlug(
      String orgSlug, List<String> gradeSlug, List<String> sectionUuid, List<String> examType);

  @Query(
      value =
          """
                          select distinct(otd.*) from offline_test_definition otd join offline_test_schedule ots on otd.id = ots.offline_test_definition_id
                          where  (cast((:gradeSlug) as varChar) is null or otd.grade_slug in (:gradeSlug))
                                          and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                                          and otd.org_slug = :orgSlug and otd.title is not null
                                          and otd.academic_year_slug in (:yearSlug)
                                          order by otd.id desc
                          """,
      nativeQuery = true)
  List<OfflineTestDefinition> getOfflineTestScheduleForReportCard(
      String orgSlug, List<String> gradeSlug, List<String> sectionUuid, List<String> yearSlug);

  @Query(
      value =
          """
                                  select distinct(otd.*) from offline_test_definition otd join offline_test_schedule ots on
                                   otd.id = ots.offline_test_definition_id
                                  where  (cast((:gradeSlug) as varChar) is null or otd.grade_slug in (:gradeSlug))
                                  and (cast((:examType) as varChar) is null or otd.id in (:examType))
                                  and (cast((:sectionUuid) as varChar) is null or otd.section_uuid in (:sectionUuid))
                                  and (cast((:subjectSlug) as varChar) is null or ots.subject_slug in (:subjectSlug))
                                  and otd.org_slug = :orgSlug and otd.title is not null and ots.published_at is not null order by id desc
                                  """,
      nativeQuery = true)
  List<OfflineTestDefinition> getExamTypes(
      String orgSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      List<String> subjectSlug,
      List<String> examType);

  @Query(
      value =
          """
                          select distinct(otd.*)  from offline_test_schedule_student otss
                          join offline_test_schedule ots on otss.offline_test_schedule_id  = ots.id
                          join offline_test_definition otd on ots.offline_test_definition_id  = otd.id
                          where student_id = :studentId and otd.org_slug = :orgSlug and otd.section_uuid =:section
                           and otd.title is not null and ots.published_at is not null
                                          """,
      nativeQuery = true)
  List<OfflineTestDefinition> getStudentExamTypes(String orgSlug, Long studentId, String section);

  @Query(
      value =
          """
                                          select distinct(otd.*)  from offline_test_schedule_student otss
                                          join offline_test_schedule ots on otss.offline_test_schedule_id  = ots.id
                                          join offline_test_definition otd on ots.offline_test_definition_id  = otd.id
                                          where student_id = :studentId and otd.org_slug = :orgSlug and otd.academic_year_slug =:academicYear
                                           and otd.title is not null and ots.published_at is not null
                                                          """,
      nativeQuery = true)
  List<OfflineTestDefinition> getStudentExamTypesByPrevStudentId(
      String orgSlug, Long studentId, String academicYear);

  @Query(
      value =
          """
                                  select distinct(otd.*)  from offline_test_schedule_student otss
                                  join offline_test_schedule ots on otss.offline_test_schedule_id  = ots.id
                                  join offline_test_definition otd on ots.offline_test_definition_id  = otd.id
                                  where student_id = :studentId and otd.org_slug = :orgSlug and otd.section_uuid =:section
                                   and otd.title is not null and (cast((:showAdmitCard) as varChar) is null or otd.show_admit_card in (:showAdmitCard))
                                                  """,
      nativeQuery = true)
  List<OfflineTestDefinition> getStudentAdmitCardExamTypes(
      String orgSlug, Long studentId, String section, Boolean showAdmitCard);

  List<OfflineTestDefinition> findAllByBoardSlugAndGradeSlugAndAcademicYearSlugAndOrgSlug(
      String boardSlug, String gradeSlug, String academicYearSlug, String orgSlug);

  List<OfflineTestDefinition> findBySectionUuidAndAssessmentSlugAndOrgSlug(
      String sectionUuid, String termSlug, String orgSlug);

  List<OfflineTestDefinition> findBySectionUuidInAndAssessmentSlug(
      List<String> sectionUuid, String assessmentSlug);

  List<OfflineTestDefinition> getAllByOrgSlug(String orgSlug);

  List<OfflineTestDefinition>
      findAllByOrgSlugAndBoardSlugAndGradeSlugAndAssessmentAndTitleAndGradeScaleSlugNotNull(
          String orgSlug,
          String boardSlug,
          String gradeSlug,
          TermAssessment assessment,
          String title);

  List<OfflineTestDefinition> findBySectionUuidAndOrgSlug(String uuid, String organization);

  List<OfflineTestDefinition> findBySectionUuidAndOrgSlugAndTermSlug(
      String uuid, String organization, String termSlug);

  List<OfflineTestDefinition> findAllByOrgSlugAndDeletedAtIsNull(String orgSlug);

  List<OfflineTestDefinition> findByOrgSlugAndSectionUuidAndAssessmentCategoryIn(
      String orgSlug, String sectionUuid, List<TermAssessmentCategory> assessmentCategory);

  Optional<OfflineTestDefinition> findByIdAndOrgSlug(Long testDefinitionId, String orgSlug);

  Optional<OfflineTestDefinition> findAllByIdAndOrgSlug(Long aLong, String orgSlug);

  Optional<OfflineTestDefinition> findByAssessmentIdAndGradeSlugAndOrgSlugAndBoardSlug(
      Long assessmentId, String gradeSlug, String orgSlug, String boardSlug);

  Optional<OfflineTestDefinition>
      findByAssessmentIdAndGradeSlugAndOrgSlugAndBoardSlugAndSectionUuid(
          Long assessmentId,
          String gradeSlug,
          String orgSlug,
          String boardSlug,
          String sectionUuid);

  List<OfflineTestDefinition> findAllByTeacherId(Long teacherId);

  @Query(
      value =
          """
                          select otd.title as title,sm.name as subjectName,sm.wexl_subject_slug as subjectSlug,otd.term_slug,otd.assessment_id,otd.assessment_category_id,
                          tac.name as testName,otss.student_id as studentId,s.roll_number as rollNo,s.class_roll_number as classRollNumber
                          ,concat(u.first_name,' ',u.last_name) as studentName,otss.marks as studentMarks, ots.marks as scheduleMarks
                          from offline_test_definition otd join offline_test_schedule ots on ots.offline_test_definition_id = otd.id
                          join subject_metadata sm on sm.id = ots.subject_metadata_id
                          join term_assessment_categories tac on tac.id = otd.assessment_category_id
                          join offline_test_schedule_student otss on otss.offline_test_schedule_id = ots.id
                          join students s on s.id = otss.student_id
                          join users u on u.id = s.user_id
                          where otd.org_slug = :orgSlug and otd.grade_slug = :gradeSlug and otd.board_slug = :boardSlug
                          and otd.section_uuid = :sectionUuid
                          group by ots.id,otd.section_uuid,otd.title,sm.name,otd.term_slug,otd.assessment_id,tac.name,otd.assessment_category_id,
                          otss.student_id,otss.marks,otss.is_attended,u.first_name,u.last_name,sm.wexl_subject_slug,ots.marks,s.roll_number,s.class_roll_number
                          order by sm.name
                                                  """,
      nativeQuery = true)
  List<StudentMarksData> getStudentMarks(
      String orgSlug, String gradeSlug, String boardSlug, String sectionUuid);

  @Query(
      value =
          """

                          select distinct(sm.wexl_subject_slug) as subjectSlug,sm.name as subjectName,ots.marks as scheduleMarks from offline_test_schedule ots join subject_metadata sm on sm.id = ots.subject_metadata_id
                          join offline_test_schedule_student otss on otss.offline_test_schedule_id = ots.id where offline_test_definition_id in (
                          select id from offline_test_definition otd  where otd.org_slug = :orgSlug and otd.grade_slug = :gradeSlug and otd.board_slug = :boardSlug
                          and otd.section_uuid = :sectionUuid)
                                                  """,
      nativeQuery = true)
  List<StudentMarksData> findSubjectsByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuid(
      String orgSlug, String gradeSlug, String boardSlug, String sectionUuid);
}
