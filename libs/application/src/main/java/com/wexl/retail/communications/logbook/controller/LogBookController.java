package com.wexl.retail.communications.logbook.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.communications.logbook.dto.LogBookDto;
import com.wexl.retail.communications.logbook.service.LogBookService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/teachers/{teacherAuthId}/logbooks")
@RequiredArgsConstructor
public class LogBookController {

  private final LogBookService logBookService;

  @IsOrgAdminOrTeacher
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void saveLogBook(
      @RequestBody LogBookDto.Request request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthId") String teacherAuthId) {
    logBookService.saveLogBook(orgSlug, request, teacherAuthId);
  }

  @GetMapping
  public List<LogBookDto.Response> getLogBook(
      LogBookDto.RequestParams requestParams,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthId") String teacherAuthId) {
    return logBookService.getLogBook(
        orgSlug,
        teacherAuthId,
        requestParams.boardSlug(),
        requestParams.gradeSlug(),
        requestParams.sectionUuid(),
        requestParams.type(),
        requestParams.studentId());
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/{id}")
  public void updateLogBook(
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthId") String teacherAuthId,
      @PathVariable("id") Long logBookId,
      @RequestBody LogBookDto.Request request) {
    logBookService.updateLogBook(orgSlug, logBookId, request, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @DeleteMapping("/{id}")
  public void deleteLogBook(@PathVariable("id") Long logBookId) {
    logBookService.deleteLogBook(logBookId);
  }

  @GetMapping("/students/{studentAuthId}")
  public List<LogBookDto.Response> getStudentLogBooks(
      @PathVariable String orgSlug,
      @PathVariable("studentAuthId") String studentAuthId,
      @RequestParam String academicYear) {
    return logBookService.getStudentLogBooks(orgSlug, studentAuthId, academicYear);
  }
}
