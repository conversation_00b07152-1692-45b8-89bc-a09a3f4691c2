package com.wexl.retail.organization.reports.controller;

import com.wexl.retail.organization.reports.dto.SubjectsMetaDataDto;
import com.wexl.retail.organization.reports.service.ReportsService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class ReportsController {

  private final ReportsService reportsService;

  @Deprecated
  public List<SubjectsMetaDataDto.Response> getSubjectsMetaDataByOrg(
      @RequestParam(value = "grade", required = false) String grade,
      @PathVariable String orgSlug,
      @RequestParam("board") String board) {
    return reportsService.getSubjectsMetaDataByOrg(orgSlug, board, grade);
  }
}
