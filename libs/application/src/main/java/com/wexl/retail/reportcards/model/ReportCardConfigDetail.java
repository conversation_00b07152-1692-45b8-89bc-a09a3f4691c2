package com.wexl.retail.reportcards.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.reportcards.dto.AssessmentEvaluationConfig;
import com.wexl.retail.term.model.TermAssessment;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "report_card_config_details")
public class ReportCardConfigDetail extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "term_assessment_id")
  private TermAssessment termAssessment;

  @Column(name = "weightage")
  private Long weightage;

  @Column(name = "seq_no")
  private Long seqNo;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "report_card_config_id")
  private ReportCardConfig reportCard;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private AssessmentEvaluationConfig assessmentEvaluationConfig;
}
