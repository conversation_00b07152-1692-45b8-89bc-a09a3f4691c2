package com.wexl.retail.organization.admin.teacher;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.student.auth.StudentAuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class PublicTeacherController {

  private final TeacherService teacherService;
  private final StudentAuthService studentAuthService;

  @PostMapping("/public/dps-teachers/external-ref")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void updateTeacherExternalRef(
      @RequestBody @Valid TeacherDto.UpdateExternalRefRequest updateExternalRefRequest) {
    try {
      teacherService.updateExternalRef(updateExternalRefRequest);
    } catch (ApiException exception) {
      throw exception;
    } catch (Exception exception) {
      log.error("Failed to update teacher external ref", exception);
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "Failed to update teacher external ref");
    }
  }

  @PostMapping("/public/dps-students/external-ref")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void updateStudentExternalRef(
      @RequestBody @Valid TeacherDto.UpdateExternalRefRequest updateExternalRefRequest) {
    try {
      studentAuthService.updateExternalRef(
          updateExternalRefRequest.orgSlug(),
          updateExternalRefRequest.email(),
          updateExternalRefRequest.externalRef());
    } catch (ApiException exception) {
      throw exception;
    } catch (Exception exception) {
      log.error("Failed to update student external ref", exception);
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "Failed to update student external ref");
    }
  }
}
