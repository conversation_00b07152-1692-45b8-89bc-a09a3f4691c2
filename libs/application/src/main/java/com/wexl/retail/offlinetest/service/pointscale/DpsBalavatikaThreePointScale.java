package com.wexl.retail.offlinetest.service.pointscale;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class DpsBalavatikaThreePointScale implements PointScale {
  @Override
  public String evaluate(int marks) {
    if (marks >= 8) {
      return "O";
    } else if (marks >= 5) {
      return "E";
    } else if (marks >= 2) {
      return "A";
    } else if (marks >= 1) {
      return "D";
    } else {
      return "B";
    }
  }

  @Override
  public String evaluate(BigDecimal marks) {
    if (marks.compareTo(new BigDecimal(8)) >= 0) {
      return "O";
    } else if (marks.compareTo(new BigDecimal(5)) >= 0) {
      return "E";
    } else if (marks.compareTo(new BigDecimal(2)) >= 0) {
      return "A";
    } else if (marks.compareTo(new BigDecimal(1)) >= 0) {
      return "D";
    } else {
      return "B";
    }
  }

  @Override
  public BigDecimal getMarks(String grade) {
    if (Objects.equals(grade, "O")) {
      return BigDecimal.valueOf(8L);
    } else if (Objects.equals(grade, "E")) {
      return BigDecimal.valueOf(5L);
    } else if (Objects.equals(grade, "A")) {
      return BigDecimal.valueOf(2L);
    } else if (Objects.equals(grade, "D")) {
      return BigDecimal.valueOf(1L);
    } else {
      return BigDecimal.valueOf(0L);
    }
  }

  @Override
  public List<String> getGradesByPointScale(String pointScale) {
    return List.of("O", "E", "A", "D", "B");
  }
}
