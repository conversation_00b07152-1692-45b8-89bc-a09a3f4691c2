package com.wexl.retail.communications.logbook.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.communications.logbook.dto.LogBookDto;
import com.wexl.retail.communications.logbook.dto.LogBookType;
import com.wexl.retail.communications.logbook.model.LogBook;
import com.wexl.retail.communications.logbook.repository.LogBookRepository;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LogBookService {
  private final UserRepository userRepository;
  private final LogBookRepository logBookRepository;
  private final ValidationUtils validationUtils;
  private final StrapiService strapiService;
  private final DateTimeUtil dateTimeUtil;
  private final StudentRepository studentRepository;

  public void saveLogBook(String orgSlug, LogBookDto.Request request, String teacherAuthId) {
    var teacher = userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug);
    List<LogBook> logBooks = new ArrayList<>();
    request
        .studentIds()
        .forEach(
            student ->
                logBooks.add(
                    LogBook.builder()
                        .title(request.title())
                        .description(request.description())
                        .boardSlug(request.boardSlug())
                        .gradeSlug(request.gradeSlug())
                        .orgSlug(orgSlug)
                        .studentId(student)
                        .attachments(request.attachment())
                        .sectionUuid(request.sectionUuids())
                        .type(request.type())
                        .dateOfObservation(
                            dateTimeUtil
                                .convertEpochToIso8601(request.dateOfObservation())
                                .toLocalDate())
                        .subjectActivity(request.subjectActivity())
                        .observerName(request.observerName())
                        .reportedTo(request.reportedTo())
                        .observedBehavior(request.observedBehavior())
                        .interpretation(request.interpretation())
                        .actionOrSupport(request.actionOrSupport())
                        .teacher(teacher)
                        .build()));

    logBookRepository.saveAll(logBooks);
  }

  public List<LogBookDto.Response> getLogBook(
      String orgSlug,
      String teacherAuthId,
      List<String> boardSlugs,
      List<String> gradeSlug,
      List<String> sectionUuids,
      LogBookType type,
      List<Long> studentIds) {

    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();

    List<Long> studentIdsList = new ArrayList<>();

    if (studentIds != null) {
      studentIdsList.addAll(studentIds);
    } else {
      studentIdsList.addAll(
          studentRepository.getStudentsByGradeSlugAndOrgSlug(gradeSlug, orgSlug).stream()
              .map(Student::getId)
              .toList());
    }

    Set<LogBook> logBookList = new HashSet<>();
    if (teacher.getRoleTemplate().getTemplate().equals(AppTemplate.ADMIN)) {
      if (boardSlugs != null
          || gradeSlug != null
          || sectionUuids != null
          || studentIds != null
          || type != null) {
        logBookList.addAll(
            logBookRepository.getLongBookData(
                orgSlug,
                boardSlugs != null ? boardSlugs : Collections.emptyList(),
                gradeSlug != null ? gradeSlug : Collections.emptyList(),
                sectionUuids != null ? sectionUuids : Collections.emptyList(),
                studentIdsList != null ? studentIdsList : Collections.emptyList(),
                type.name()));
      } else {
        logBookList.addAll(logBookRepository.findAllByOrgSlug(orgSlug));
      }
    } else if (teacher.getRoleTemplate().getTemplate().equals(AppTemplate.TEACHER)) {
      logBookList.addAll(
          logBookRepository.getLongBookData(
              orgSlug,
              boardSlugs != null ? boardSlugs : Collections.emptyList(),
              gradeSlug != null ? gradeSlug : Collections.emptyList(),
              sectionUuids != null ? sectionUuids : Collections.emptyList(),
              studentIdsList != null ? studentIdsList : Collections.emptyList(),
              type.name()));
    }
    if (logBookList.isEmpty()) {
      return new ArrayList<>();
    }
    var allGrades = strapiService.getAllGrades();
    var allEduBoards = strapiService.getAllBoards();
    return logBookList.stream()
        .map(
            logBook -> {
              Student student = validationUtils.isStudentValid(logBook.getStudentId());
              String studentName =
                  student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName();

              User logBookTeacher = logBook.getTeacher();
              String teacherName =
                  logBookTeacher.getFirstName() + " " + logBookTeacher.getLastName();
              var board = validationUtils.findBoardBySlug(allEduBoards, logBook.getBoardSlug());
              var grade = validationUtils.findGradeBySlug(allGrades, logBook.getGradeSlug());
              var sectionData = validationUtils.findSectionByUuid(logBook.getSectionUuid());
              return new LogBookDto.Response(
                  logBook.getId(),
                  logBook.getTitle(),
                  logBook.getDescription(),
                  logBook.getGradeSlug(),
                  grade.getName(),
                  logBook.getBoardSlug(),
                  board.getAssetName(),
                  logBook.getStudentId(),
                  studentName,
                  logBook.getSectionUuid(),
                  sectionData.getName(),
                  logBookTeacher.getId(),
                  teacherName,
                  logBook.getCreatedAt().getTime(),
                  logBook.getAttachments(),
                  logBook.getType(),
                  logBook.getDateOfObservation() != null
                      ? DateTimeUtil.convertIso8601ToEpoch(
                          logBook.getDateOfObservation().atStartOfDay())
                      : null,
                  logBook.getSubjectActivity(),
                  logBook.getObserverName(),
                  logBook.getReportedTo(),
                  logBook.getObservedBehavior(),
                  logBook.getInterpretation(),
                  logBook.getActionOrSupport());
            })
        .toList();
  }

  public void updateLogBook(
      String orgSlug, Long logBookId, LogBookDto.Request request, String teacherAuthId) {
    var teacher = userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug);
    var logBook = getLogBook(logBookId);
    logBook.setTitle(request.title());
    logBook.setDescription(request.description());
    logBook.setType(request.type());
    logBook.setSectionUuid(request.sectionUuids());
    logBook.setAttachments(logBook.getAttachments());
    logBook.setBoardSlug(request.boardSlug());
    logBook.setGradeSlug(request.gradeSlug());
    logBook.setDateOfObservation(
        dateTimeUtil.convertEpochToIso8601(request.dateOfObservation()).toLocalDate());
    logBook.setSubjectActivity(request.subjectActivity());
    logBook.setObserverName(request.observerName());
    logBook.setReportedTo(request.reportedTo());
    logBook.setObservedBehavior(request.observedBehavior());
    logBook.setInterpretation(request.interpretation());
    logBook.setActionOrSupport(request.actionOrSupport());
    logBook.setTeacher(teacher);

    logBookRepository.save(logBook);
  }

  private LogBook getLogBook(Long logBookId) {
    var logBook = logBookRepository.findById(logBookId);
    if (logBook.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.LogBookId",
          new String[] {Long.toString(logBookId)});
    }

    return logBook.get();
  }

  public void deleteLogBook(Long logBookId) {
    var logBook = getLogBook(logBookId);
    logBookRepository.delete(logBook);
  }

  public List<LogBookDto.Response> getStudentLogBooks(
      String orgSlug, String studentAuthId, String academicYear) {
    var user = validationUtils.isValidUser(studentAuthId);
    var student = validationUtils.isStudentValid(user.getStudentInfo().getId());

    if (!student.getAcademicYearSlug().equals(academicYear)) {
      var prevStudentId = student.getPrevStudentId();
      if (prevStudentId == null) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.StudentFind.AcademicYear");
      }
      student = validationUtils.isStudentValid(prevStudentId);
    }

    var logBooks =
        logBookRepository.findAllByOrgSlugAndStudentIdOrderByCreatedAtDesc(
            orgSlug, student.getId());
    if (logBooks.isEmpty()) {
      return Collections.emptyList();
    }

    var allGrades = strapiService.getAllGrades();
    var allEduBoards = strapiService.getAllBoards();
    var studentFullName =
        student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName();

    return logBooks.stream()
        .map(
            logBook -> {
              var logBookTeacher = logBook.getTeacher();
              var teacherName = logBookTeacher.getFirstName() + " " + logBookTeacher.getLastName();

              var board = validationUtils.findBoardBySlug(allEduBoards, logBook.getBoardSlug());
              var grade = validationUtils.findGradeBySlug(allGrades, logBook.getGradeSlug());
              var sectionData = validationUtils.findSectionByUuid(logBook.getSectionUuid());

              return new LogBookDto.Response(
                  logBook.getId(),
                  logBook.getTitle(),
                  logBook.getDescription(),
                  logBook.getGradeSlug(),
                  grade.getName(),
                  logBook.getBoardSlug(),
                  board.getAssetName(),
                  logBook.getStudentId(),
                  studentFullName,
                  logBook.getSectionUuid(),
                  sectionData.getName(),
                  logBookTeacher.getId(),
                  teacherName,
                  logBook.getCreatedAt().getTime(),
                  logBook.getAttachments(),
                  logBook.getType(),
                  logBook.getDateOfObservation() != null
                      ? DateTimeUtil.convertIso8601ToEpoch(
                          logBook.getDateOfObservation().atStartOfDay())
                      : null,
                  logBook.getSubjectActivity(),
                  logBook.getObserverName(),
                  logBook.getReportedTo(),
                  logBook.getObservedBehavior(),
                  logBook.getInterpretation(),
                  logBook.getActionOrSupport());
            })
        .toList();
  }
}
