package com.wexl.retail.organization.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.model.Gender;
import com.wexl.retail.student.registration.dto.StudentAttributeData;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StudentRequest {
  @NotBlank private String userName;
  @NotBlank private String firstName;
  private String lastName;
  @NotBlank private String schoolName;

  private String email;
  private String rollNumber;
  private String mobileNumber;

  @JsonProperty("academicYear")
  @NotBlank
  private String academicYearSlug;

  @NotNull private Gender gender;

  @JsonProperty("grade")
  @NotBlank
  private String gradeSlug;

  @JsonProperty("board")
  private String boardSlug;

  private String password;
  private String parentFirstName;
  private String parentLastName;
  private String parentEmail;
  private String parentMobileNumber;

  private String section;

  private String orgSlug;

  private String guid;

  private String externalRef;
  private String crStudentUserName;
  private StudentAttributeData attributes;

  @JsonProperty("role_template")
  private RoleTemplate roleTemplate;

  @JsonProperty("country_code")
  private String countryCode;

  @JsonProperty("class_roll_number")
  private String classRollNumber;

  @JsonProperty("is_fee_paid")
  private boolean isFeePaid = true;
}
