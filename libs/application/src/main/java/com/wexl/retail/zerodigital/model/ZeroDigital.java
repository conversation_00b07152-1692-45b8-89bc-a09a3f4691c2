package com.wexl.retail.zerodigital.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "zero_digital")
public class ZeroDigital extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String chapterSlug;

  private String chapterName;

  @Enumerated(EnumType.STRING)
  private ZeroDigitalStatus status;

  private LocalDateTime completionDate;

  private LocalDateTime plannedDate;

  private String orgSlug;

  private String gradeSlug;

  private String gradeName;

  private String boardSlug;

  private String subjectSlug;

  private String subjectName;

  private Long testDefinitionId;

  private Long testScheduleId;

  private String zdSlug;

  private String sectionUuid;

  private String sectionName;

  private LocalDateTime examConductedAt;

  private LocalDateTime WorkSheetIssuedAt;

  private LocalDateTime BadgesIssuedAt;

  private LocalDateTime workSheetsCompleted;
}
