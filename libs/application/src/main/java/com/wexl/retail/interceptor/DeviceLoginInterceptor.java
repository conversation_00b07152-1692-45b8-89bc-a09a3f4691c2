package com.wexl.retail.interceptor;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Slf4j
@Component
public class DeviceLoginInterceptor implements HandlerInterceptor {

  private final AuthService authService;
  private final UserRepository userRepository;

  @Value("${app.device.single.organizations}")
  private String[] singleDeviceOrgs;

  @Value("${app.device.switch.organizations}")
  private String[] switchDeviceOrgs;

  public DeviceLoginInterceptor(@Lazy AuthService authService, UserRepository userRepository) {
    this.authService = authService;
    this.userRepository = userRepository;
  }

  private boolean isSameDevice(String guid, User student) {
    return guid != null && guid.equals(student.getGuid());
  }

  private boolean shouldUseSingleDevice(String organization) {
    return Arrays.asList(singleDeviceOrgs).contains(organization);
  }

  private boolean canSwitchDevice(String organization) {
    return Arrays.asList(switchDeviceOrgs).contains(organization);
  }

  private Boolean successiveApiCallRestrictions(User user, @NonNull HttpServletResponse response) {
    var student = userRepository.getUserByAuthUserId(user.getAuthUserId());

    if (Objects.isNull(student.getGuid()) || isSameDevice(user.getGuid(), student)) {
      return Boolean.TRUE;
    }

    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
    return Boolean.FALSE;
  }

  @Override
  public boolean preHandle(
      @NonNull HttpServletRequest request,
      @NonNull HttpServletResponse response,
      @NonNull Object handler) {

    if (SecurityContextHolder.getContext().getAuthentication().isAuthenticated()
        && SecurityContextHolder.getContext()
            .getAuthentication()
            .getName()
            .equals("anonymousUser")) {
      return Boolean.TRUE;
    }

    var user = authService.getUserDetails();

    if ((AuthUtil.isTeacher(user))) {
      return Boolean.TRUE;
    }

    if (!shouldUseSingleDevice(user.getOrganization())
        && !canSwitchDevice(user.getOrganization())) {
      return Boolean.TRUE;
    }

    return successiveApiCallRestrictions(user, response);
  }
}
