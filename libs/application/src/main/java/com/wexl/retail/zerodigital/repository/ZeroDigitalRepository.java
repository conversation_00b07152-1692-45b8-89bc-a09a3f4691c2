package com.wexl.retail.zerodigital.repository;

import com.wexl.retail.zerodigital.model.ZeroDigital;
import com.wexl.retail.zerodigital.model.ZeroDigitalStatus;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ZeroDigitalRepository extends JpaRepository<ZeroDigital, Long> {

  @Query(
      "SELECT zd.chapterSlug FROM ZeroDigital zd WHERE zd.orgSlug = :orgSlug and zd.sectionUuid =:section")
  List<String> findChapterSlugByOrgSlugAndSectionAndDeletedAtIsNull(String orgSlug, String section);

  List<ZeroDigital> findAllByOrgSlug(String orgSlug);

  List<ZeroDigital>
      findAllByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuidAndSubjectSlugAndDeletedAtIsNull(
          String orgSlug,
          String boardSlug,
          String gradeSlug,
          String sectionUuid,
          String subjectSlug);

  List<ZeroDigital> findAllByOrgSlugAndBoardSlugAndGradeSlugAndSubjectSlugAndDeletedAtIsNull(
      String orgSlug, String boardSlug, String gradeSlug, String subjectSlug);

  List<ZeroDigital>
      findAllByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuidAndSubjectSlugAndDeletedAtIsNullOrderByChapterName(
          String orgSlug,
          String boardSlug,
          String gradeSlug,
          String sectionUuid,
          String subjectSlug);

  List<ZeroDigital> findAllByOrgSlugAndSectionUuidAndDeletedAtIsNullOrderByChapterName(
      String orgSlug, String sectionUuid);

  Optional<ZeroDigital> findByIdAndOrgSlug(Long zdId, String orgSlug);

  Optional<ZeroDigital> findByIdAndChapterSlugAndOrgSlug(Long zdId, String zdSlug, String orgSlug);

  List<ZeroDigital> findByZdSlugAndTestDefinitionIdIsNull(String zdSlug);

  List<ZeroDigital> findByOrgSlugAndChapterSlugIn(String orgSlug, List<String> chapter);

  List<ZeroDigital> findByOrgSlugAndChapterSlugInAndStatus(
      String orgSlug, List<String> chapter, ZeroDigitalStatus status);

  List<ZeroDigital> findAllByTestScheduleIdIn(List<Long> scheduleTestIds);

  List<ZeroDigital> findAllByTestDefinitionIdIsNull(Pageable pageable);
}
