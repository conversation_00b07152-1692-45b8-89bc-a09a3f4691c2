package com.wexl.retail.organization.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Gender;
import jakarta.validation.constraints.NotNull;

public record StudentDto() {

  public record UpdateRollNumberRequest(@NotNull @JsonProperty("roll_number") String rollNumber) {}

  public record UpdatePasswordRequest(@NotNull @JsonProperty("new_password") String newPassword) {}

  public record UpdateUserDetailsRequest(
      @JsonProperty("mobile_number") String mobileNumber,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      @JsonProperty("institute_name") String instituteName,
      Gender gender,
      String country,
      String address,
      String city,
      String state) {}
}
