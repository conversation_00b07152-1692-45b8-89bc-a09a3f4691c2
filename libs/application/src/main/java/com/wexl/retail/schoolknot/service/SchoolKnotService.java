package com.wexl.retail.schoolknot.service;

import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.UserRole;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.schoolknot.dto.SchoolKnotDto;
import com.wexl.retail.section.dto.request.SectionCreateRequest;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.teacher.auth.TeacherAuthService;
import com.wexl.retail.teacher.auth.TeacherAuthTransformer;
import com.wexl.retail.teacher.auth.TeacherSignupRequest;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SchoolKnotService {
  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  private final StudentAuthService studentAuthService;
  private final UserRoleHelper userRoleHelper;
  private final SectionRepository sectionRepository;
  private final TeacherAuthService teacherAuthService;
  private final TeacherAuthTransformer teacherAuthTransformer;
  private final SectionService sectionService;

  public void createStudent(SchoolKnotDto.schoolKnotStudentRequest request) {
    studentAuthService.createStudent(buildStudentRequest(request), request.orgSlug());
  }

  private StudentRequest buildStudentRequest(SchoolKnotDto.schoolKnotStudentRequest request) {
    var section =
        sectionRepository
            .findByUuid(UUID.fromString(request.sectionUuid()))
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound"));
    var roleTemplate =
        userRoleHelper.findRoleTemplateByOrgSlug(request.orgSlug(), UserRole.ROLE_ISTUDENT);
    return StudentRequest.builder()
        .email(request.email())
        .firstName(request.firstName())
        .lastName(request.lastName())
        .gender(request.gender())
        .mobileNumber(request.mobileNumber())
        .userName(request.userName())
        .orgSlug(request.orgSlug())
        .password(request.password())
        .gradeSlug(request.gradeSlug())
        .roleTemplate(roleTemplate)
        .section(section.getName())
        .rollNumber(request.rollNumber())
        .classRollNumber(request.admissionNumber())
        .academicYearSlug(latestAcademicYear)
        .password(request.password())
        .build();
  }

  public void createTeacher(SchoolKnotDto.schoolKnotTeacherRequest request) {
    teacherAuthService.createTeacherInBulk(buidTeacherRequest(request));
  }

  private TeacherSignupRequest buidTeacherRequest(SchoolKnotDto.schoolKnotTeacherRequest request) {
    return TeacherSignupRequest.builder()
        .emailAddress(request.email())
        .firstName(request.firstName())
        .lastName(request.lastName())
        .mobileNumber(request.mobileNumber())
        .password(request.password())
        .orgSlug(request.orgSlug())
        .mobileNumber(request.mobileNumber())
        .roleType(UserRole.ROLE_ITEACHER.name())
        .roleTemplate(teacherAuthTransformer.getRoleTemplateByOrgSlug(request.orgSlug()))
        .termsAndConditions(true)
        .build();
  }

  public void createSection(SchoolKnotDto.schoolKnotSectionRequest request) {
    sectionService.createSection(request.orgSlug(), buildSectionResponse(request));
  }

  private SectionCreateRequest buildSectionResponse(
      SchoolKnotDto.schoolKnotSectionRequest request) {
    return SectionCreateRequest.builder()
        .gradeSlug(request.gradeSlug())
        .boardSlug(request.boardSlug())
        .name(request.sectionName())
        .build();
  }
}
