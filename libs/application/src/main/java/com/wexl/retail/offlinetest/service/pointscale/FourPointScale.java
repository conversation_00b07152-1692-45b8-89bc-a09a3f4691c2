package com.wexl.retail.offlinetest.service.pointscale;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class FourPointScale implements PointScale {

  @Override
  public String evaluate(int marks) {
    if (marks >= 81) {
      return "A";
    } else if (marks >= 61) {
      return "B";
    } else if (marks >= 32) {
      return "C";
    } else {
      return "D";
    }
  }

  @Override
  public String evaluate(BigDecimal marks) {
    if (marks.compareTo(new BigDecimal(81)) >= 0) {
      return "A";
    } else if (marks.compareTo(new BigDecimal(61)) >= 0) {
      return "B";
    } else if (marks.compareTo(new BigDecimal(32)) >= 0) {
      return "C";
    } else {
      return "D";
    }
  }

  @Override
  public BigDecimal getMarks(String grade) {
    if (Objects.equals(grade, "A")) {
      return BigDecimal.valueOf(81L);
    } else if (Objects.equals(grade, "B")) {
      return BigDecimal.valueOf(61L);
    } else if (Objects.equals(grade, "C")) {
      return BigDecimal.valueOf(32L);
    } else return BigDecimal.valueOf(20L);
  }

  @Override
  public final List<String> getGradesByPointScale(String pointScale) {
    return List.of("A", "B", "C", "D");
  }
}
