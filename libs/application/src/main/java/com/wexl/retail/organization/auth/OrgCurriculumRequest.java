package com.wexl.retail.organization.auth;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrgCurriculumRequest {

  private String boardSlug;

  private String gradeSlug;

  private String subjectSlug;

  private String orgSlug;

  private String subjectDisplayName;
}
