package com.wexl.retail.communications.homework.service;

import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.communications.homework.dto.HomeworkDto;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.model.Notification;
import com.wexl.retail.notifications.model.NotificationType;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class HomeworkService {
  private final NotificationsService notificationService;
  private final UserRepository userRepository;
  private final NotificationRepository notificationRepository;
  private final EventNotificationService eventNotificationService;
  private final TeacherRepository teacherRepository;
  private final DateTimeUtil dateTimeUtil;

  public void createHomeworkNotification(
      String orgSlug, HomeworkDto.HomeworkRequest request, String teacherAuthId) {
    List<Teacher> teachers =
        teacherRepository.getSectionTeachersBySectionUuids(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Long> teacherIds = teachers.stream().map(Teacher::getId).toList();
    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title(request.title())
            .message(request.message())
            .sectionUuids(request.sectionUuids())
            .studentIds(request.studentIds())
            .teacherIds(teacherIds)
            .attachment(request.attachment())
            .link(request.link())
            .notificationType(com.wexl.retail.notifications.model.NotificationType.SECTION)
            .feature(CommunicationFeature.HOMEWORK)
            .fromDate(
                LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli())
            .toDate(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli())
            .build();

    notificationService.createNotificationByTeacher(
        orgSlug, notificationRequest, teacherAuthId, false);
    eventNotificationService.sendPushNotificationForSection(
        orgSlug,
        notificationRequest.message(),
        notificationRequest.title(),
        notificationRequest.sectionUuids(),
        notificationRequest.studentIds(),
        teacherAuthId);
  }

  public HomeworkDto.HomeworkResponse getHomeworkNotifications(
      String orgSlug, String teacherAuthId) {
    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();
    List<Notification> homeworkNotifications;
    if (UserRoleHelper.get().isOrgAdmin(teacher.getUserInfo())) {
      homeworkNotifications =
          notificationRepository.findAllByOrgSlugAndFeatureOrderByCreatedAtDesc(
              orgSlug, CommunicationFeature.HOMEWORK);
    } else {
      homeworkNotifications =
          notificationRepository.findAllByCreatedByAndOrgSlugAndFeatureOrderByCreatedAtDesc(
              teacher, orgSlug, CommunicationFeature.HOMEWORK);
    }
    var notificationResponse =
        notificationService.buildNotificationResponse(homeworkNotifications, 100);
    return HomeworkDto.HomeworkResponse.builder().homeworkResponse(notificationResponse).build();
  }

  public HomeworkDto.StudentHomeworkResponse getStudentHomeworkNotifications(
      String orgSlug, String studentAuthId) {
    var student =
        userRepository.findByAuthUserIdAndOrganization(studentAuthId, orgSlug).getStudentInfo();
    var studentHomeworkNotifications =
        notificationRepository
            .findByOrgSlugAndFeatureAndStudentNotifications_StudentAndDeletedAtIsNullOrderByCreatedAtDesc(
                orgSlug, CommunicationFeature.HOMEWORK, student);
    var notificationResponse =
        notificationService.getStudentNotificationResponses(
            studentHomeworkNotifications, student.getId());
    return HomeworkDto.StudentHomeworkResponse.builder()
        .homeworkResponse(notificationResponse)
        .build();
  }

  public void updateHomeworkNotification(
      String orgSlug, Long notificationId, HomeworkDto.HomeworkRequest request) {
    var newNotificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title(request.title())
            .message(request.message())
            .sectionUuids(request.sectionUuids())
            .studentIds(request.studentIds())
            .attachment(request.attachment())
            .link(request.link())
            .notificationType(NotificationType.SECTION)
            .build();
    notificationService.editNotificationByTeacher(
        orgSlug, newNotificationRequest, null, notificationId);
  }

  public List<HomeworkDto.HomeWorkDetails> getAllOrgHomeworks(
      String orgSlug, Long fromDate, Long toDate) {
    var fDate = dateTimeUtil.convertEpochToTimestamp(fromDate);
    var tDate = dateTimeUtil.convertEpochToTimestamp(toDate);
    var orgHomeWorks =
        notificationRepository.getAllOrgNotificationsByCommunicationFeature(
            orgSlug, CommunicationFeature.HOMEWORK.name(), fDate, tDate);
    List<HomeworkDto.HomeWorkDetails> response = new ArrayList<>();
    for (HomeworkDetails homework : orgHomeWorks) {
      var createdAt = dateTimeUtil.convertTimeStampToLong(homework.getDate());
      response.add(
          HomeworkDto.HomeWorkDetails.builder()
              .teacherName(homework.getTeacherName())
              .date(createdAt)
              .title(homework.getTitle())
              .message(homework.getMessage())
              .section(homework.getSection())
              .attachments(homework.getAttachments())
              .build());
    }
    return response.stream()
        .sorted(Comparator.comparing(HomeworkDto.HomeWorkDetails::date).reversed())
        .toList();
  }
}
