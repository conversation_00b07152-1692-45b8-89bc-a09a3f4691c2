package com.wexl.retail.organization.reports.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record SubjectsMetaDataDto() {

  public record GradesData(@JsonProperty("grades") List<Grades> grades) {}

  public record Grades(
      @JsonProperty("grade_name") String name,
      @JsonProperty("display_name") String displayName,
      @JsonProperty("grade_slug") String gradeSlug,
      List<Subjects> subjects) {}

  public record Subjects(
      @JsonProperty("display_name") String displayName,
      @JsonProperty("wexl_subject_slug") String wexlSubjectSlug,
      @JsonProperty("type") String type) {}

  @Builder
  public record Response(
      @JsonProperty("name") String gradeName,
      @JsonProperty("slug") String gradeSlug,
      @JsonProperty("subjects") List<SubjectResponse> subjectResponses) {}

  @Builder
  public record SubjectResponse(
      String name, @JsonProperty("slug") String subjectSlug, String type) {}
}
