package com.wexl.retail.offlinetest.repository;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

public interface StudentReportCard {
  @JsonProperty("exam_name")
  String getExamName();

  @JsonProperty("subject_name")
  String getSubjectName();

  @JsonProperty("total_marks")
  Long getTotalMarks();

  @JsonProperty("marks_scored")
  String getMarksScored();

  @JsonProperty("subject_slug")
  String getSubjectSlug();

  @JsonProperty("exam_start_date")
  LocalDateTime getExamStartDate();
}
