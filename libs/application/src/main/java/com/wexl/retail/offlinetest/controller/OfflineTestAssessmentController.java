package com.wexl.retail.offlinetest.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.offlinetest.dto.OfflineTestAssessmentDto;
import com.wexl.retail.offlinetest.service.OfflineTestAssessmentService;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class OfflineTestAssessmentController {

  private final OfflineTestAssessmentService offlineTestAssessmentService;
  private final OfflineTestScheduleService offlineTestScheduleService;

  @IsOrgAdminOrTeacher
  @GetMapping("/offline-test-assessments")
  public List<OfflineTestAssessmentDto.Response> getOfflineAssessments() {
    return offlineTestAssessmentService.getOfflineAssessments();
  }
}
