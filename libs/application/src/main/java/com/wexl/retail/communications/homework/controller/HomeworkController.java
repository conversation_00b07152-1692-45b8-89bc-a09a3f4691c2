package com.wexl.retail.communications.homework.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.communications.homework.dto.HomeworkDto;
import com.wexl.retail.communications.homework.service.HomeworkService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class HomeworkController {
  private final HomeworkService homeworkService;

  @IsOrgAdminOrTeacher
  @PostMapping("/teachers/{teacherAuthId}/homeworks")
  @ResponseStatus(HttpStatus.CREATED)
  public void createHomeworkNotification(
      @RequestBody HomeworkDto.HomeworkRequest request,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthId") String teacherAuthId) {

    homeworkService.createHomeworkNotification(orgSlug, request, teacherAuthId);
  }

  @GetMapping("/teachers/{teacherAuthId}/homeworks")
  public HomeworkDto.HomeworkResponse getHomeworkNotifications(
      @PathVariable String orgSlug, @PathVariable("teacherAuthId") String teacherAuthId) {
    return homeworkService.getHomeworkNotifications(orgSlug, teacherAuthId);
  }

  @IsOrgAdminOrTeacher
  @PutMapping("/teachers/{teacherAuthId}/homeworks/{notificationId}")
  public void updateHomeworkNotification(
      @PathVariable String orgSlug,
      @PathVariable("notificationId") Long notificationId,
      @RequestBody HomeworkDto.HomeworkRequest request) {
    homeworkService.updateHomeworkNotification(orgSlug, notificationId, request);
  }

  @GetMapping("/students/{studentAuthId}/homeworks")
  public HomeworkDto.StudentHomeworkResponse gegStudentHomeworkNotifications(
      @PathVariable String orgSlug, @PathVariable("studentAuthId") String studentAuthId) {
    return homeworkService.getStudentHomeworkNotifications(orgSlug, studentAuthId);
  }

  @GetMapping("/homeworks-dashboard")
  public List<HomeworkDto.HomeWorkDetails> getAllOrgHomeworks(
      @PathVariable String orgSlug, @RequestParam Long fromDate, @RequestParam Long toDate) {
    return homeworkService.getAllOrgHomeworks(orgSlug, fromDate, toDate);
  }
}
