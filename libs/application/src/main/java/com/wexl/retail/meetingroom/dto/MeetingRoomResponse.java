package com.wexl.retail.meetingroom.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.schedules.domain.MeetingType;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class MeetingRoomResponse {

  private Long id;
  private String name;

  @JsonProperty("display_name")
  private String displayName;

  @Enumerated(EnumType.STRING)
  private MeetingType type;

  @JsonProperty("created_date")
  private long createdDate;

  @JsonProperty("host_link")
  private String hostLink;

  @JsonProperty("join_link")
  private String joinLink;
}
