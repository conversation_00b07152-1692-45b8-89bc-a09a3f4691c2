package com.wexl.retail.offlinetest.dto;

import java.util.List;
import lombok.Builder;

public class InterOverAllReportDto {
  @Builder
  public record Response(LowerGradeReportDto.Header header, LowerGradeReportDto.Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String dateOfBirth,
      String orgSlug,
      String termSlug,
      String gradeSlug,
      String gradePerformanceText,
      String gradingScale,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTable thirdTable,
      FourthTable fourthTable,
      Attendance attendance) {}

  @Builder
  public record FirstTable(String title, List<FirstTableMarks> marks, Totals totals) {}

  @Builder
  public record SecondTable(String title, List<SecondTableMarks> marks) {}

  @Builder
  public record ThirdTable(String title, ThirdTableMarks marks) {}

  @Builder
  public record FourthTable(String title, List<FirstTableMarks> marks) {}

  @Builder
  public record Totals(Long annualExam, Long total, String grade, Double overallPercentage) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, Double attendancePercentage, String remarks) {}

  @Builder
  public record FirstTableMarks(
      Long sNo,
      String subject,
      String theoryMarks,
      String practicalMarks,
      String total,
      Long totalMarksScored,
      Long totalExamMarks,
      String highestMarks,
      Long seqNo) {}

  @Builder
  public record SecondTableMarks(String subjectName, String term1Grade) {}

  @Builder
  public record ThirdTableMarks(
      List<SubTable> subTable1, List<SubTable> subTable2, List<SubTable> subTable3) {}

  @Builder
  public record FourthTableMarks(String subjectName, String term1Grade) {}

  @Builder
  public record SubTable(String subjectName, String descriptive, String term1Grade) {}

  @Builder
  public record TableMarks(
      List<FirstTableMarks> firstTableMarks,
      List<SecondTableMarks> secondTableMarks,
      ThirdTableMarks thirdTableMarks,
      List<FirstTableMarks> fourthTableMarks) {}

  @Builder
  public record CoScholasticArea(List<ThirdTableMarks> thirdTableMarks) {}
  ;

  public record InterTerm1CoScholastic(
      String subjectTitle, String subjectName, String descriptive, String term1Grade) {}
}
