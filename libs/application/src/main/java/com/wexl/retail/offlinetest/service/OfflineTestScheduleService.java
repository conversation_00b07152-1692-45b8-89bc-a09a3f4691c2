package com.wexl.retail.offlinetest.service;

import static com.wexl.retail.offlinetest.service.MarksUtil.toMarksString;
import static java.util.Comparator.comparing;
import static java.util.Comparator.nullsLast;
import static java.util.Objects.isNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import com.wexl.retail.metrics.reportcards.ReportCardDto;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.model.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.offlinetest.dto.OfflineTestScheduleDto;
import com.wexl.retail.offlinetest.model.*;
import com.wexl.retail.offlinetest.repository.*;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSubjects;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import com.wexl.retail.subjects.service.SubjectsMetaDataService;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentCategoryRepository;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class OfflineTestScheduleService {

  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final OfflineTestScheduleRepository offlineTestScheduleRepository;
  private final ValidationUtils validationUtils;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final SectionService sectionService;
  private final DateTimeUtil dateTimeUtil;
  private final TeacherRepository teacherRepository;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final NotificationsService notificationsService;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceAndRemarksRepository;
  private final OfflineTestAssessmentRepository offlineTestAssessmentRepository;
  private final OfflineTestAssessmentService offlineTestAssessmentService;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final UserService userService;
  private final SubjectsMetaDataService subjectsMetaDataService;
  private final List<ClassTeacherSubjectHandler> classTeacherSubjectHandlers;
  private final TermAssessmentRepository termAssessmentRepository;
  private final TermAssessmentCategoryRepository assessmentCategoryRepository;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final TermAssessmentCategoryRepository termAssessmentCategoryRepository;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final String DEFAULT_ASSESSMENT_CATEGORY = "Default";
  private final UserRoleHelper userRoleHelper;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ExamRepository examRepository;

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  @Value("classpath:section-marks-entry-the779154.json")
  private Resource sectionMarksResource;

  private final MessageTemplateRepository messageTemplateRepository;

  @Value("${app.messageTemplate.examMarkTemplateId:}")
  private String examMarksMessageTemplate;

  private final String gillcoOrgSlug = "gil923272";
  private final List<String> scholarsOrgSlugs = List.of("the677317", "the782042", "the779154");
  private final List<String> gradeSlugs = List.of("i", "ii");

  public void saveOfflineTestDefinition(
      String orgSlug, OfflineTestScheduleDto.Request request, String teacherAuthId) {
    var user = validationUtils.isValidUser(teacherAuthId);
    final List<OfflineTestDefinition> offlineTestDefinitions = new ArrayList<>();
    Map<UUID, Section> sectionMap = getSectionMap(request.sectionUuid());
    request
        .sectionUuid()
        .forEach(
            sectionUuid -> {
              Section section = sectionMap.get(UUID.fromString(sectionUuid));
              var testDefinition =
                  buildOfflineTestScheduleStudents(section, request, user, orgSlug);
              offlineTestDefinitions.add(testDefinition);
            });

    var offlineTestDefinitionsUpdated =
        offlineTestDefinitionRepository.saveAll(offlineTestDefinitions);

    final List<OfflineTestScheduleStudentAttendance> offlineTestScheduleStudentAttendances =
        new ArrayList<>();
    offlineTestDefinitionsUpdated.forEach(
        offlineTestDefinition -> {
          Section section = sectionMap.get(UUID.fromString(offlineTestDefinition.getSectionUuid()));
          offlineTestScheduleStudentAttendances.addAll(
              buildOfflineTestScheduleStudentAttendanceAndRemarks(offlineTestDefinition, section));
        });
    offlineTestScheduleStudentAttendanceAndRemarksRepository.saveAll(
        offlineTestScheduleStudentAttendances);
  }

  private Map<UUID, Section> getSectionMap(List<String> sectionUuids) {
    List<UUID> sectionUuidList = new ArrayList<>();
    sectionUuids.forEach(uuid -> sectionUuidList.add(UUID.fromString(uuid)));
    return sectionRepository.findAllByUuidIn(sectionUuidList).stream()
        .collect(Collectors.toMap(Section::getUuid, section -> section));
  }

  public void updateOfflineTestSchedule(
      Long offlineTestScheduleId, OfflineTestScheduleDto.Subjects subjects) {
    OfflineTestSchedule offlineTestSchedule = validateOfflineTestSchedule(offlineTestScheduleId);
    offlineTestSchedule.setMarks(subjects.marks());
    offlineTestSchedule.setShowRc(subjects.isReportCard());
    offlineTestSchedule.setConsiderPercentage(subjects.isPercentage());
    offlineTestSchedule.setExamStartTime(
        Objects.isNull(subjects.examStartTime())
            ? offlineTestSchedule.getExamStartTime()
            : dateTimeUtil.convertEpochToLocalTime(subjects.examStartTime()));
    offlineTestSchedule.setExamEndTime(
        Objects.isNull(subjects.examEndTime())
            ? offlineTestSchedule.getExamEndTime()
            : dateTimeUtil.convertEpochToLocalTime(subjects.examEndTime()));
    offlineTestScheduleRepository.save(offlineTestSchedule);
  }

  private OfflineTestDefinition buildOfflineTestScheduleStudents(
      Section section, OfflineTestScheduleDto.Request request, User user, String orgSlug) {

    OfflineTestDefinition testDefinition = new OfflineTestDefinition();

    LocalDateTime fDate = dateTimeUtil.convertEpochToIso8601Legacy(request.examStartDate());
    LocalDateTime tDate = dateTimeUtil.convertEpochToIso8601Legacy(request.examEndDate());
    var termAssessment = termAssessmentRepository.findById(request.assessmentId()).orElseThrow();
    var assessmentCategory =
        assessmentCategoryRepository
            .findByIdAndTermAssessment(request.assessmentCategoryId(), termAssessment)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.AssessmentCategoryNotFound"));
    testDefinition.setAcademicYearSlug(request.academicYearSlug());
    testDefinition.setOrgSlug(orgSlug);
    testDefinition.setExamEndDate(tDate);
    testDefinition.setExamStartDate(fDate);
    testDefinition.setTitle(request.title());
    testDefinition.setBoardSlug(request.boardSlug());
    testDefinition.setBoardName(request.boardName());
    testDefinition.setGradeSlug(request.gradeSlug());
    testDefinition.setGradeName(request.gradeName());
    testDefinition.setTermSlug(request.termSlug());
    testDefinition.setAssessment(termAssessment);
    testDefinition.setAssessmentCategory(assessmentCategory);
    testDefinition.setGradeScaleSlug(request.gradeScaleSlug());
    testDefinition.setSectionUuid(section.getUuid().toString());
    testDefinition.setTeacherId(user.getTeacherInfo().getId());
    testDefinition.setCreatedBy(user.getAuthUserId());
    testDefinition.setReportCardTemplate(
        Objects.nonNull(request.reportCardTemplate())
            ? reportCardTemplateRepository
                .findByIdAndOrgSlug(request.reportCardTemplate(), orgSlug)
                .orElseThrow()
            : null);
    testDefinition.setShowStudents(request.showStudents());
    testDefinition.setOfflineTestScheduleSchedule(
        buildOfflineTestSchedule(testDefinition, request.subjects(), section));
    return testDefinition;
  }

  private List<OfflineTestScheduleStudentAttendance>
      buildOfflineTestScheduleStudentAttendanceAndRemarks(
          OfflineTestDefinition testDefinition, Section section) {
    if (!Boolean.FALSE.equals(checkIfTestDefinitionExist(testDefinition))) {
      return new ArrayList<>();
    }
    var studentIdList = studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
    List<OfflineTestScheduleStudentAttendance> studentList = new ArrayList<>();
    studentIdList.forEach(
        student ->
            studentList.add(
                OfflineTestScheduleStudentAttendance.builder()
                    .studentId(student.getId())
                    .offlineTestDefinition(testDefinition)
                    .build()));
    return studentList;
  }

  private boolean checkIfTestDefinitionExist(OfflineTestDefinition testDefinition) {
    long count =
        offlineTestScheduleStudentAttendanceAndRemarksRepository.countByOfflineTestDefinition(
            testDefinition);
    return count > 0;
  }

  private List<OfflineTestSchedule> buildOfflineTestSchedule(
      OfflineTestDefinition testDefinition,
      List<OfflineTestScheduleDto.Subjects> subjects,
      Section section) {
    List<OfflineTestSchedule> offlineTestScheduleDetails = new ArrayList<>();
    subjects.forEach(
        subject -> {
          var subjectMetaData =
              subjectsMetaDataService.validateSubjectId(subject.subjectMetaDataId());
          LocalDateTime date = dateTimeUtil.convertEpochToIso8601Legacy(subject.date());
          OfflineTestSchedule testSchedule = new OfflineTestSchedule();
          testSchedule.setScheduledAt(date);
          testSchedule.setMarks(subject.marks());
          testSchedule.setConsiderPercentage(subject.isPercentage());
          testSchedule.setShowRc(subject.isReportCard());
          testSchedule.setSubjectsMetaData(subjectMetaData);
          testSchedule.setOfflineTestDefinition(testDefinition);
          testSchedule.setExamStartTime(
              Objects.nonNull(subject.examStartTime())
                  ? dateTimeUtil.convertEpochToLocalTime(subject.examStartTime())
                  : null);
          testSchedule.setExamEndTime(
              Objects.nonNull(subject.examEndTime())
                  ? dateTimeUtil.convertEpochToLocalTime(subject.examEndTime())
                  : null);
          testSchedule.setOfflineTestScheduleStudents(
              buildOfflineTestScheduleStudents(section, testSchedule));
          testSchedule.setPublishedAt(null);

          offlineTestScheduleDetails.add(testSchedule);
        });
    return offlineTestScheduleDetails;
  }

  private List<OfflineTestScheduleStudent> buildOfflineTestScheduleStudents(
      Section section, OfflineTestSchedule offlineTestScheduleDetails) {

    final SubjectsMetaData subjectsMetaData = offlineTestScheduleDetails.getSubjectsMetaData();
    final List<SubjectsMetadataStudents> subjectsMetadataStudents =
        subjectsMetaData.getSubjectsMetadataStudents();
    final Set<Long> studentIds =
        subjectsMetadataStudents.stream()
            .map(SubjectsMetadataStudents::getStudentId)
            .collect(Collectors.toSet());

    final List<Student> allById =
        studentRepository.getByIdsAndSection(new ArrayList<>(studentIds), section.getId());

    final List<OfflineTestScheduleStudentAttendance> attendanceList =
        allById.stream()
            .map(
                student ->
                    OfflineTestScheduleStudentAttendance.builder()
                        .studentId(student.getId())
                        .presentDays(null)
                        .remarks(null)
                        .build())
            .collect(Collectors.toList());
    offlineTestScheduleStudentAttendanceAndRemarksRepository.saveAll(attendanceList);

    final List<OfflineTestScheduleStudent> studentList =
        allById.stream()
            .map(
                student ->
                    OfflineTestScheduleStudent.builder()
                        .studentId(student.getId())
                        .orgSlug(student.getUserInfo().getOrganization())
                        .offlineTestScheduleDetails(offlineTestScheduleDetails)
                        .termAssessmentCategory(
                            offlineTestScheduleDetails
                                .getOfflineTestDefinition()
                                .getAssessmentCategory())
                        .build())
            .toList();
    return studentList;
  }

  public List<OfflineTestScheduleDto.Response> getOfflineTestDefinition(
      String teacherAuthId,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      List<String> subjectSlug,
      String orgSlug,
      String academicYearSlug) {
    var user = validationUtils.isValidUser(teacherAuthId);
    var adminTeachers = teacherRepository.getAllAdminsByOrg(orgSlug);
    var teacherIds = adminTeachers.stream().map(Teacher::getId).toList();

    if (AuthUtil.isOrgAdmin(user) || userRoleHelper.isManager(user)) {
      var offlineTestDefinition =
          offlineTestDefinitionRepository.getOfflineTestScheduleByTeacherId(
              teacherIds, boardSlug, gradeSlug, sectionUuid, academicYearSlug);
      return buildOfflineTestDefinitionResponse(offlineTestDefinition, subjectSlug);
    }
    return teacherSubjects(user, subjectSlug, boardSlug, gradeSlug, sectionUuid, orgSlug);
  }

  private List<OfflineTestScheduleDto.Response> teacherSubjects(
      User user,
      List<String> subjectSlug,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      String orgSlug) {
    var teacherId = user.getTeacherInfo().getId();
    var sections = user.getTeacherInfo().getSections();
    List<TeacherSubjects> teacherSubjects =
        teacherSubjectsRepository.findByTeacher(user.getAuthUserId());
    List<OfflineTestSchedule> offlineTestScheduleList = new ArrayList<>();

    for (Section section : sections) {
      List<SubjectsMetaData> subjectsMetaData = new ArrayList<>();

      if (section.getClassTeacher() != null
          && section.getClassTeacher().getId() == (teacherId)
          && !classTeacherSubjectHandlers.isEmpty()) {
        final List<SubjectsMetaData> byOrgSlugAndGradeSlugAndBoardSlug =
            classTeacherSubjectHandlers.getFirst().getClassTeacherPreferredSubjectSlugs(section);
        subjectsMetaData.addAll(byOrgSlugAndGradeSlugAndBoardSlug);
      } else {
        var subjectSlugs =
            teacherSubjects.stream()
                .filter(x -> x.getSection().getUuid().equals(section.getUuid()))
                .map(TeacherSubjects::getSubject)
                .toList();
        subjectsMetaData.addAll(
            subjectsMetaDataRepository.findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
                orgSlug, subjectSlugs, section.getGradeSlug(), section.getBoardSlug()));
      }

      List<Long> subjectsMetadataIds =
          subjectsMetaData.stream().map(SubjectsMetaData::getId).toList();
      List<OfflineTestSchedule> schedules =
          offlineTestScheduleRepository.getOfflineTestSchedulesBySubjectMetadataAndSection(
              subjectsMetadataIds, section.getUuid().toString());
      offlineTestScheduleList.addAll(schedules);
    }

    if (offlineTestScheduleList.isEmpty()) {
      return Collections.emptyList();
    }

    return buildResponse(offlineTestScheduleList, subjectSlug, boardSlug, gradeSlug, sectionUuid);
  }

  private List<OfflineTestScheduleDto.Response> buildResponse(
      List<OfflineTestSchedule> offlineTestScheduleList,
      List<String> subjectSlugs,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid) {
    List<OfflineTestScheduleDto.Response> responseList = new ArrayList<>();
    var offlineTestDefinition =
        offlineTestScheduleList.stream()
            .map(OfflineTestSchedule::getOfflineTestDefinition)
            .distinct()
            .toList();
    offlineTestDefinition.forEach(
        testDefinition -> {
          var offlineTestSchedules =
              offlineTestScheduleList.stream()
                  .filter(x -> x.getOfflineTestDefinition().getId().equals(testDefinition.getId()))
                  .toList();
          offlineTestSchedules =
              filterSubjectsData(
                  subjectSlugs, boardSlug, gradeSlug, sectionUuid, offlineTestSchedules);
          if (offlineTestSchedules.isEmpty()) {
            return;
          }

          responseList.add(
              OfflineTestScheduleDto.Response.builder()
                  .offlineTestDefinitionId(testDefinition.getId())
                  .academicYearSlug(testDefinition.getAcademicYearSlug())
                  .boardSlug(testDefinition.getBoardSlug())
                  .gradeSlug(testDefinition.getGradeSlug())
                  .gradeName(testDefinition.getGradeName())
                  .boardName(testDefinition.getBoardName())
                  .sectionUuid(testDefinition.getSectionUuid())
                  .sectionName(validateSection(testDefinition.getSectionUuid()).getName())
                  .examStartDate(
                      DateTimeUtil.convertIso8601ToEpoch(testDefinition.getExamStartDate()))
                  .examEndDate(DateTimeUtil.convertIso8601ToEpoch(testDefinition.getExamEndDate()))
                  .title(testDefinition.getTitle())
                  .subjects(buildSubjectResponse(offlineTestSchedules))
                  .gradeScaleSlug(testDefinition.getGradeScaleSlug())
                  .notificationStatus(
                      Objects.nonNull(testDefinition.getNotificationStatus())
                          && testDefinition.getNotificationStatus())
                  .build());
        });
    responseList.sort(
        Comparator.comparing(
                OfflineTestScheduleDto.Response::examStartDate,
                Comparator.nullsLast(Comparator.naturalOrder()))
            .reversed());
    return responseList;
  }

  private List<OfflineTestSchedule> filterSubjectsData(
      List<String> subjectSlugs,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      List<OfflineTestSchedule> offlineTestSchedules) {
    if (!subjectSlugs.isEmpty()) {
      offlineTestSchedules =
          offlineTestSchedules.stream()
              .filter(
                  schedule ->
                      subjectSlugs.contains(schedule.getSubjectsMetaData().getWexlSubjectSlug()))
              .toList();
    }
    if (!boardSlug.isEmpty()) {
      offlineTestSchedules =
          offlineTestSchedules.stream()
              .filter(
                  schedule ->
                      boardSlug.contains(schedule.getOfflineTestDefinition().getBoardSlug()))
              .toList();
    }
    if (!gradeSlug.isEmpty()) {
      offlineTestSchedules =
          offlineTestSchedules.stream()
              .filter(
                  schedule ->
                      gradeSlug.contains(schedule.getOfflineTestDefinition().getGradeSlug()))
              .toList();
    }
    if (!sectionUuid.isEmpty()) {
      offlineTestSchedules =
          offlineTestSchedules.stream()
              .filter(
                  schedule ->
                      sectionUuid.contains(schedule.getOfflineTestDefinition().getSectionUuid()))
              .toList();
    }
    return offlineTestSchedules;
  }

  private List<OfflineTestScheduleDto.Response> buildOfflineTestDefinitionResponse(
      List<OfflineTestDefinition> testDefinitionsList, List<String> filterSubject) {
    List<OfflineTestScheduleDto.Response> responseList = new ArrayList<>();
    testDefinitionsList.forEach(
        testDefinition -> {
          var filterSubjectsByInput =
              filterSubjects(testDefinition.getOfflineTestScheduleSchedule(), filterSubject);
          if (!filterSubject.isEmpty() && filterSubjectsByInput.isEmpty()) {
            return;
          }
          responseList.add(
              OfflineTestScheduleDto.Response.builder()
                  .offlineTestDefinitionId(testDefinition.getId())
                  .academicYearSlug(testDefinition.getAcademicYearSlug())
                  .boardSlug(testDefinition.getBoardSlug())
                  .gradeSlug(testDefinition.getGradeSlug())
                  .gradeName(testDefinition.getGradeName())
                  .boardName(testDefinition.getBoardName())
                  .sectionUuid(testDefinition.getSectionUuid())
                  .sectionName(validateSection(testDefinition.getSectionUuid()).getName())
                  .examStartDate(
                      DateTimeUtil.convertIso8601ToEpoch(testDefinition.getExamStartDate()))
                  .examEndDate(DateTimeUtil.convertIso8601ToEpoch(testDefinition.getExamEndDate()))
                  .title(testDefinition.getTitle())
                  .subjects(buildSubjectResponse(filterSubjectsByInput))
                  .notificationStatus(
                      Objects.nonNull(testDefinition.getNotificationStatus())
                          && testDefinition.getNotificationStatus())
                  .reportCardTemplateId(
                      Objects.nonNull(testDefinition.getReportCardTemplate())
                          ? testDefinition.getReportCardTemplate().getId()
                          : null)
                  .reportCardTemplateName(
                      Objects.nonNull(testDefinition.getReportCardTemplate())
                          ? testDefinition.getReportCardTemplate().getName()
                          : null)
                  .showStudents(testDefinition.getShowStudents())
                  .showAdmitCard(testDefinition.getShowAdmitCard())
                  .gradeScaleSlug(testDefinition.getGradeScaleSlug())
                  .build());
        });
    responseList.sort(
        Comparator.comparing(
                OfflineTestScheduleDto.Response::examStartDate,
                Comparator.nullsLast(Comparator.naturalOrder()))
            .reversed());
    return responseList;
  }

  private List<OfflineTestSchedule> filterSubjects(
      List<OfflineTestSchedule> allSubjects, List<String> filterSubject) {
    List<OfflineTestSchedule> subjectList = new ArrayList<>();

    if (filterSubject.isEmpty()) {
      return allSubjects;
    }

    filterSubject.forEach(
        subject ->
            subjectList.addAll(
                allSubjects.stream()
                    .filter(
                        x ->
                            x.getSubjectsMetaData() != null
                                && x.getSubjectsMetaData().getWexlSubjectSlug().contains(subject))
                    .toList()));

    return subjectList;
  }

  private List<OfflineTestScheduleDto.Subjects> buildSubjectResponse(
      List<OfflineTestSchedule> offlineTestScheduleDetails) {
    return offlineTestScheduleDetails.stream()
        .filter(details -> details.getDeletedAt() == null)
        .map(
            details -> {
              var subjectMetaData = details.getSubjectsMetaData();
              return OfflineTestScheduleDto.Subjects.builder()
                  .date(DateTimeUtil.convertIso8601ToEpoch(details.getScheduledAt()))
                  .id(details.getId())
                  .subjectMetaDataId(subjectMetaData != null ? subjectMetaData.getId() : null)
                  .subjectName(
                      subjectMetaData != null
                          ? subjectMetaData.getName()
                          : (details.getSubjectsMetaData() != null
                              ? details.getSubjectsMetaData().getWexlSubjectSlug()
                              : null))
                  .subjectSlug(
                      subjectMetaData != null
                          ? subjectMetaData.getWexlSubjectSlug()
                          : (details.getSubjectsMetaData() != null
                              ? details.getSubjectsMetaData().getWexlSubjectSlug()
                              : null))
                  .marks(details.getMarks())
                  .publishedAt(
                      details.getPublishedAt() != null
                          ? DateTimeUtil.convertIso8601ToEpoch(details.getPublishedAt())
                          : null)
                  .isPercentage(details.getConsiderPercentage())
                  .isReportCard(details.getShowRc())
                  .build();
            })
        .toList();
  }

  public void publishOfflineTestSchedule(long offlineTestScheduleId, boolean publishStatus) {
    var offlineTestSchedule = validateOfflineTestSchedule(offlineTestScheduleId);
    if (publishStatus) {
      offlineTestSchedule.setPublishedAt(LocalDateTime.now());
    } else {
      offlineTestSchedule.setPublishedAt(null);
    }
    offlineTestScheduleRepository.save(offlineTestSchedule);
  }

  public OfflineTestScheduleDto.Response getOfflineTestScheduleStudents(
      Long offlineTestScheduleDetailsId) {
    var offlineTestSchedule = validateOfflineTestSchedule(offlineTestScheduleDetailsId);
    return buildOfflineTestScheduleStudentsResponse(offlineTestSchedule);
  }

  private OfflineTestScheduleDto.Response buildOfflineTestScheduleStudentsResponse(
      OfflineTestSchedule offlineTestScheduleDetails) {
    var offlineTestDefinition = offlineTestScheduleDetails.getOfflineTestDefinition();
    var subjectMetaData =
        subjectsMetaDataRepository
            .findByOrgSlugAndWexlSubjectSlugAndNameAndGradeSlugAndBoardSlugAndStatusAndDeletedAtIsNull(
                offlineTestDefinition.getOrgSlug(),
                offlineTestScheduleDetails.getSubjectsMetaData().getWexlSubjectSlug(),
                offlineTestScheduleDetails.getSubjectsMetaData().getName(),
                offlineTestDefinition.getGradeSlug(),
                offlineTestDefinition.getBoardSlug(),
                Boolean.TRUE);
    return OfflineTestScheduleDto.Response.builder()
        .type(subjectMetaData == null ? null : subjectMetaData.getSubjectsTypeEnum())
        .subjectsCategoryEnum(subjectMetaData == null ? null : subjectMetaData.getCategoryEnum())
        .offlineTestScheduleId(offlineTestScheduleDetails.getId())
        .academicYearSlug(offlineTestDefinition.getAcademicYearSlug())
        .boardSlug(offlineTestDefinition.getBoardSlug())
        .gradeSlug(offlineTestDefinition.getGradeSlug())
        .gradeName(offlineTestDefinition.getGradeName())
        .boardName(offlineTestDefinition.getBoardName())
        .sectionUuid(offlineTestDefinition.getSectionUuid())
        .sectionName(validateSection(offlineTestDefinition.getSectionUuid()).getName())
        .subjectName(offlineTestScheduleDetails.getSubjectsMetaData().getName())
        .subjectSlug(offlineTestScheduleDetails.getSubjectsMetaData().getWexlSubjectSlug())
        .gradeScaleSlug(offlineTestScheduleDetails.getOfflineTestDefinition().getGradeScaleSlug())
        .examStartDate(
            offlineTestDefinition.getExamStartDate() == null
                ? null
                : DateTimeUtil.convertIso8601ToEpoch(offlineTestDefinition.getExamStartDate()))
        .examEndDate(
            offlineTestDefinition.getExamEndDate() == null
                ? null
                : DateTimeUtil.convertIso8601ToEpoch(offlineTestDefinition.getExamEndDate()))
        .title(offlineTestDefinition.getTitle())
        .totalMarks(offlineTestScheduleDetails.getMarks())
        .studentsDetails(buildStudent(offlineTestScheduleDetails, offlineTestDefinition))
        .build();
  }

  private List<OfflineTestScheduleDto.StudentsResponse> buildStudent(
      OfflineTestSchedule offlineTestScheduleDetails, OfflineTestDefinition testDefinition) {
    List<OfflineTestScheduleDto.StudentsResponse> studentsList = new ArrayList<>();
    List<OfflineTestScheduleStudent> offlineTestScheduleStudents =
        offlineTestScheduleDetails.getOfflineTestScheduleStudents();
    var sectionWiseMarksJsonResponse =
        getSectionMarksEntry(testDefinition.getOrgSlug(), offlineTestScheduleDetails.getId());
    offlineTestScheduleStudents.forEach(
        student -> {
          var studentDetails = validationUtils.isStudentValid(student.getStudentId());
          var userDetails = studentDetails.getUserInfo();
          if (userDetails != null && userDetails.getDeletedAt() == null) {
            studentsList.add(
                OfflineTestScheduleDto.StudentsResponse.builder()
                    .marks(toMarksString(student.getMarks()))
                    .marks1(toMarksString(student.getMarks()))
                    .marks1Grade(getGrade(student.getMarks(), offlineTestScheduleDetails))
                    .remarks(student.getRemarks())
                    .result(
                        calculateResult(
                            student.getMarks(),
                            offlineTestScheduleDetails.getMarks(),
                            student.getIsAttended(),
                            testDefinition.getOrgSlug()))
                    .isAttended(student.getIsAttended())
                    .student_auth_id(userDetails.getAuthUserId())
                    .name(userDetails.getFirstName() + " " + userDetails.getLastName())
                    .studentId(student.getStudentId())
                    .classRollNumber(
                        studentDetails.getClassRollNumber() == null
                            ? "-"
                            : studentDetails.getClassRollNumber())
                    .orgSlug(student.getOrgSlug())
                    .admissionNumber(studentDetails.getRollNumber())
                    .sectionWiseMarks(
                        buildSectionWiseMarks(
                            sectionWiseMarksJsonResponse, student.getSectionWiseMarks()))
                    .build());
          }
        });

    return studentsList.stream()
        .sorted(
            comparing(
                student ->
                    student.classRollNumber() != null
                        ? getStudentClassRollNumber(student.classRollNumber())
                        : null,
                nullsLast(Comparator.naturalOrder())))
        .toList();
  }

  private List<OfflineTestScheduleDto.SectionWiseMarks> buildSectionWiseMarks(
      ReportCardDto.SectionDetails sectionWiseMarksJsonResponse,
      List<OfflineTestScheduleDto.SectionWiseMarks> sectionWiseMarks) {

    if (sectionWiseMarksJsonResponse == null || sectionWiseMarksJsonResponse.sections().isEmpty()) {
      return Objects.isNull(sectionWiseMarks) ? new ArrayList<>() : sectionWiseMarks;
    }

    var jsonSections = sectionWiseMarksJsonResponse.sections();

    if (sectionWiseMarks == null) {
      return jsonSections.stream()
          .map(
              jsonSection ->
                  OfflineTestScheduleDto.SectionWiseMarks.builder()
                      .section(jsonSection.name())
                      .totalMarks(BigDecimal.valueOf(jsonSection.marks()))
                      .marks(null)
                      .build())
          .toList();
    }

    if (jsonSections.size() == sectionWiseMarks.size()) {
      return sectionWiseMarks;
    }

    Map<String, OfflineTestScheduleDto.SectionWiseMarks> marksMap =
        sectionWiseMarks.stream()
            .collect(
                Collectors.toMap(
                    OfflineTestScheduleDto.SectionWiseMarks::section, Function.identity()));

    return jsonSections.stream()
        .map(
            jsonSection ->
                marksMap.getOrDefault(
                    jsonSection.name(),
                    OfflineTestScheduleDto.SectionWiseMarks.builder()
                        .section(jsonSection.name())
                        .totalMarks(BigDecimal.valueOf(jsonSection.marks()))
                        .marks(null)
                        .build()))
        .toList();
  }

  private int getStudentClassRollNumber(String classRollNumber) {
    try {
      return Integer.parseInt(classRollNumber);
    } catch (NumberFormatException ex) {
      return 0;
    }
  }

  private String calculateResult(
      BigDecimal marksScored,
      Long offlineTestScheduleDetailsMarks,
      Boolean isAttended,
      String orgSlug) {
    if (marksScored == null || Boolean.FALSE.equals(isAttended)) {
      return "-";
    }

    if (offlineTestScheduleDetailsMarks == null || offlineTestScheduleDetailsMarks == 0) {
      throw new IllegalArgumentException("offlineTestScheduleDetailsMarks cannot be null or zero.");
    }
    MathContext mc = new MathContext(4, RoundingMode.HALF_UP);
    BigDecimal marksTotal = BigDecimal.valueOf(offlineTestScheduleDetailsMarks.doubleValue());
    BigDecimal percentage = marksScored.multiply(BigDecimal.valueOf(100)).divide(marksTotal, mc);
    if (orgSlug.equals("sai681502")) {
      return pointScaleEvaluator.evaluate("saiSeniorPointScale", percentage);
    }
    if ("mah177".equals(orgSlug) && percentage.compareTo(BigDecimal.valueOf(32.5)) == 0) {
      return "Pass";
    }
    return pointScaleEvaluator.evaluate("default", percentage);
  }

  public String getGrade(BigDecimal marks) {
    return marks == null ? null : pointScaleEvaluator.evaluate("3point", marks);
  }

  public String getGrade(BigDecimal marks, OfflineTestSchedule testSchedule) {
    if (marks == null) {
      return null;
    }

    String gradeScaleSlug = determineGradeScaleSlug(testSchedule);
    return pointScaleEvaluator.evaluate(gradeScaleSlug, marks);
  }

  public BigDecimal getMarksByGrade(
      OfflineTestScheduleDto.Students student, OfflineTestSchedule testSchedule) {
    if (student.marks1Grade() == null) {
      return null;
    }

    String gradeScaleSlug = determineGradeScaleSlug(testSchedule);
    return pointScaleEvaluator.marks(gradeScaleSlug, student.marks1Grade());
  }

  private String determineGradeScaleSlug(OfflineTestSchedule testSchedule) {
    SubjectsMetaData metadata = testSchedule.getSubjectsMetaData();
    SubjectsCategoryEnum category = metadata.getCategoryEnum();
    SubjectsTypeEnum type = metadata.getSubjectsTypeEnum();

    boolean isCoScholasticOptionalOrCurricular =
        SubjectsCategoryEnum.CO_SCHOLASTIC.equals(category)
            && (SubjectsTypeEnum.OPTIONAL.equals(type)
                || SubjectsTypeEnum.CURRICULAR_ACTIVITIES.equals(type));

    return isCoScholasticOptionalOrCurricular
        ? testSchedule.getOfflineTestDefinition().getGradeScaleSlug()
        : "3point";
  }

  public void saveOfflineTestScheduleStudents(
      Long offlineTestScheduleId, OfflineTestScheduleDto.StudentsRequest studentsList) {
    var offlineTestSchedule = validateOfflineTestSchedule(offlineTestScheduleId);
    var offlineStudents = offlineTestSchedule.getOfflineTestScheduleStudents();
    studentsList
        .students()
        .forEach(
            students -> {
              var data =
                  offlineStudents.stream()
                      .filter(x -> x.getStudentId().equals(students.studentId()))
                      .findFirst();
              var subjectsMetaData = offlineTestSchedule.getSubjectsMetaData();
              BigDecimal gradeMarks = null;
              if (data.isPresent()) {
                if (students.marks1Grade() != null) {
                  gradeMarks = getMarksByGrade(students, offlineTestSchedule);
                }
                var totalMarks =
                    Objects.nonNull(subjectsMetaData)
                            && SubjectsCategoryEnum.CO_SCHOLASTIC.equals(
                                subjectsMetaData.getCategoryEnum())
                        ? gradeMarks
                        : getTotalMarks(students, gradeMarks);
                var marks1 =
                    (students.marks() == null && gradeMarks != null)
                        ? gradeMarks
                        : students.marks();
                final OfflineTestScheduleStudent offlineTestScheduleStudent = data.get();
                offlineTestScheduleStudent.setMarks(totalMarks);
                offlineTestScheduleStudent.setIsAttended(students.isAttended());
                offlineTestScheduleStudent.setRemarks(students.remarks());
                offlineTestScheduleStudent.setMarks1(marks1);
                offlineTestScheduleStudent.setMarks2(students.marks2());
                offlineTestScheduleStudent.setMarks3(students.marks3());
                offlineTestScheduleStudent.setSectionWiseMarks(students.sectionWiseMarks());
              }
            });
    offlineTestScheduleRepository.save(offlineTestSchedule);
  }

  private BigDecimal getTotalMarks(OfflineTestScheduleDto.Students student, BigDecimal gradeMarks) {
    if (student.marks1() == null) {
      return null;
    }
    return student
        .marks1()
        .add(student.marks2() == null ? BigDecimal.valueOf(0) : student.marks2())
        .add(student.marks3() == null ? BigDecimal.valueOf(0) : student.marks3());
  }

  public OfflineTestSchedule validateOfflineTestSchedule(Long offlineTestScheduleId) {
    var offlineTestSchedule = offlineTestScheduleRepository.findById(offlineTestScheduleId);
    if (offlineTestSchedule.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Invalid.OfflineTestSchedule",
          new String[] {Long.toString(offlineTestScheduleId)});
    }
    return offlineTestSchedule.get();
  }

  public List<OfflineTestScheduleDto.Subjects> getSubjectByDefinitionAndScheduleId(
      Long offlineTestDefinitionId, Long offlineTestScheduleId) {
    OfflineTestDefinition offlineTestDefinition =
        validateOfflineTestDefinition(offlineTestDefinitionId);
    if (offlineTestScheduleId == null) {

      return buildingOfflineTestScheduled(offlineTestDefinition.getOfflineTestScheduleSchedule());
    }
    return buildingOfflineTestScheduled(
        offlineTestDefinition.getOfflineTestScheduleSchedule().stream()
            .filter(schedule -> schedule.getId().equals(offlineTestScheduleId))
            .toList());
  }

  public List<OfflineTestScheduleDto.Subjects> buildingOfflineTestScheduled(
      List<OfflineTestSchedule> offlineTestScheduleList) {
    return offlineTestScheduleList.stream()
        .filter(subject -> subject.getDeletedAt() == null)
        .map(
            offlineTestSchedule -> {
              var subjectMetaData = offlineTestSchedule.getSubjectsMetaData();
              return OfflineTestScheduleDto.Subjects.builder()
                  .id(offlineTestSchedule.getId())
                  .subjectName(
                      subjectMetaData != null
                          ? subjectMetaData.getName()
                          : offlineTestSchedule.getSubjectsMetaData().getName())
                  .subjectSlug(
                      subjectMetaData != null
                          ? subjectMetaData.getWexlSubjectSlug()
                          : offlineTestSchedule.getSubjectsMetaData().getWexlSubjectSlug())
                  .subjectMetaDataId(subjectMetaData != null ? subjectMetaData.getId() : null)
                  .marks(offlineTestSchedule.getMarks())
                  .date(DateTimeUtil.convertIso8601ToEpoch(offlineTestSchedule.getScheduledAt()))
                  .publishedAt(
                      Objects.nonNull(offlineTestSchedule.getPublishedAt())
                          ? DateTimeUtil.convertIso8601ToEpoch(offlineTestSchedule.getPublishedAt())
                          : null)
                  .isPercentage(offlineTestSchedule.getConsiderPercentage())
                  .isReportCard(offlineTestSchedule.getShowRc())
                  .examStartTime(
                      Objects.nonNull(offlineTestSchedule.getExamStartTime())
                          ? dateTimeUtil.convertLocalTimeToEpoch(
                              offlineTestSchedule.getExamStartTime())
                          : null)
                  .examEndTime(
                      Objects.nonNull(offlineTestSchedule.getExamEndTime())
                          ? dateTimeUtil.convertLocalTimeToEpoch(
                              offlineTestSchedule.getExamEndTime())
                          : null)
                  .build();
            })
        .toList();
  }

  public void deleteSubjectsByOfflineTestScheduleId(Long offlineTestScheduleId) {
    OfflineTestSchedule offlineTestSchedule = validateOfflineTestSchedule(offlineTestScheduleId);
    offlineTestSchedule.setDeletedAt(new Date());
    offlineTestScheduleRepository.save(offlineTestSchedule);
  }

  public OfflineTestScheduleDto.Response getOfflineTestSchedule(Long offlineTestDefinitionId) {
    var testDefinition = validateOfflineTestDefinition(offlineTestDefinitionId);
    var offlineTestSubjects = buildSubjectResponse(testDefinition.getOfflineTestScheduleSchedule());
    return OfflineTestScheduleDto.Response.builder()
        .offlineTestDefinitionId(offlineTestDefinitionId)
        .academicYearSlug(testDefinition.getAcademicYearSlug())
        .boardSlug(testDefinition.getBoardSlug())
        .gradeSlug(testDefinition.getGradeSlug())
        .gradeName(testDefinition.getGradeName())
        .boardName(testDefinition.getBoardName())
        .sectionUuid(testDefinition.getSectionUuid())
        .termSlug(testDefinition.getTermSlug())
        .assessmentCategoryId(testDefinition.getAssessmentCategory().getId())
        .gradeScaleSlug(testDefinition.getGradeScaleSlug())
        .assessmentSlug(testDefinition.getAssessment().getSlug())
        .sectionName(validateSection(testDefinition.getSectionUuid()).getName())
        .examStartDate(
            testDefinition.getExamStartDate() == null
                ? null
                : DateTimeUtil.convertIso8601ToEpoch(testDefinition.getExamStartDate()))
        .examEndDate(
            testDefinition.getExamStartDate() == null
                ? null
                : DateTimeUtil.convertIso8601ToEpoch(testDefinition.getExamEndDate()))
        .title(testDefinition.getTitle())
        .subjects(offlineTestSubjects)
        .reportCardTemplateId(
            Objects.nonNull(testDefinition.getReportCardTemplate())
                ? testDefinition.getReportCardTemplate().getId()
                : null)
        .reportCardTemplateName(
            Objects.nonNull(testDefinition.getReportCardTemplate())
                ? testDefinition.getReportCardTemplate().getName()
                : null)
        .showStudents(testDefinition.getShowStudents())
        .showAdmitCard(testDefinition.getShowAdmitCard())
        .build();
  }

  public OfflineTestDefinition validateOfflineTestDefinition(Long offlineTestDefinitionId) {
    var offlineTestDefinition = offlineTestDefinitionRepository.findById(offlineTestDefinitionId);
    if (offlineTestDefinition.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Invalid.OfflineTestSchedule",
          new String[] {Long.toString(offlineTestDefinitionId)});
    }
    return offlineTestDefinition.get();
  }

  private Section validateSection(String sectionUuid) {
    Optional<Section> optionalSection = sectionRepository.findByUuid(UUID.fromString(sectionUuid));
    if (optionalSection.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound", new String[] {sectionUuid});
    }
    return optionalSection.get();
  }

  public void addSubjectsToOfflineTestDefinition(
      Long offlineTestDefinitionId, OfflineTestScheduleDto.Subjects subject) {
    LocalDateTime date =
        LocalDateTime.ofInstant(Instant.ofEpochMilli(subject.date()), ZoneId.systemDefault());
    OfflineTestSchedule existingSchedule =
        offlineTestScheduleRepository.findBySubjectAndOfflineTestDefinitionId(
            subject.subjectSlug(), offlineTestDefinitionId, subject.subjectName());

    if (existingSchedule != null) {
      existingSchedule.setDeletedAt(null);
      existingSchedule.setMarks(subject.marks());
      existingSchedule.setScheduledAt(date);
      existingSchedule.setExamStartTime(
          Objects.isNull(subject.examStartTime())
              ? null
              : dateTimeUtil.convertEpochToLocalTime(subject.examStartTime()));
      existingSchedule.setExamEndTime(
          Objects.isNull(subject.examEndTime())
              ? null
              : dateTimeUtil.convertEpochToLocalTime(subject.examEndTime()));
      offlineTestScheduleRepository.save(existingSchedule);
    } else {
      OfflineTestDefinition offlineTestDefinition =
          validateOfflineTestDefinition(offlineTestDefinitionId);
      var section = validationUtils.findSectionByUuid(offlineTestDefinition.getSectionUuid());
      OfflineTestSchedule offlineTestSchedule =
          buildOfflineTestSchedule(subject, date, section, offlineTestDefinition);
      offlineTestScheduleRepository.save(offlineTestSchedule);
    }
  }

  private OfflineTestSchedule buildOfflineTestSchedule(
      OfflineTestScheduleDto.Subjects subject,
      LocalDateTime date,
      Section section,
      OfflineTestDefinition offlineTestDefinition) {
    var subjectMetaData = subjectsMetaDataService.validateSubjectId(subject.subjectMetaDataId());
    OfflineTestSchedule testSchedule = new OfflineTestSchedule();
    testSchedule.setScheduledAt(date);
    testSchedule.setMarks(subject.marks());
    testSchedule.setExamStartTime(
        Objects.nonNull(subject.examStartTime())
            ? dateTimeUtil.convertEpochToLocalTime(subject.examStartTime())
            : null);
    testSchedule.setExamEndTime(
        Objects.nonNull(subject.examEndTime())
            ? dateTimeUtil.convertEpochToLocalTime(subject.examEndTime())
            : null);
    testSchedule.setShowRc(subject.isReportCard());
    testSchedule.setConsiderPercentage(subject.isPercentage());
    testSchedule.setSubjectsMetaData(subjectMetaData);
    testSchedule.setOfflineTestDefinition(offlineTestDefinition);
    testSchedule.setOfflineTestScheduleStudents(
        buildOfflineTestScheduleStudents(section, testSchedule));
    testSchedule.setPublishedAt(null);

    return testSchedule;
  }

  @Transactional
  public void sendOfflineTestDefinitionNotifications(
      String orgSlug, String teacherAuthId, Long offlineTestDefinitionId) {

    var offlineTestDefinition = validateOfflineTestDefinition(offlineTestDefinitionId);
    validateTestNotification(offlineTestDefinition, teacherAuthId);
    var testSchedules = offlineTestDefinition.getOfflineTestScheduleSchedule();
    List<Long> students =
        testSchedules.stream()
            .map(OfflineTestSchedule::getOfflineTestScheduleStudents)
            .flatMap(Collection::stream)
            .map(OfflineTestScheduleStudent::getStudentId)
            .toList();
    if (students.isEmpty()) {
      return;
    }
    var notificationRequest = buildNotificationRequest(offlineTestDefinition, students);
    notificationsService.createNotificationByTeacher(
        orgSlug, notificationRequest, teacherAuthId, false);
    offlineTestDefinition.setNotificationStatus(Boolean.TRUE);
    offlineTestDefinitionRepository.save(offlineTestDefinition);
  }

  private NotificationDto.NotificationRequest buildNotificationRequest(
      OfflineTestDefinition testDefinition, List<Long> students) {

    var message =
        NotificationDto.TestMessage.builder()
            .id(testDefinition.getId())
            .message(
                String.format("%s results are released. Kindly check", testDefinition.getTitle()))
            .build();
    var testMessageJsonString = StringUtils.EMPTY;
    try {
      testMessageJsonString = new ObjectMapper().writeValueAsString(message);
    } catch (Exception e) {
      log.error("There was an error while converting offline test message to send notification");
    }
    var messageTemplate =
        messageTemplateRepository.findBySmsDltTemplateId(examMarksMessageTemplate);
    return NotificationDto.NotificationRequest.builder()
        .studentIds(students)
        .title("Offline Test Notification")
        .message(testMessageJsonString)
        .messageTemplateId(messageTemplate.map(MessageTemplate::getId).orElse(null))
        .notificationType(NotificationType.INDIVIDUAL)
        .build();
  }

  private void validateTestNotification(
      OfflineTestDefinition offlineTestDefinition, String teacherAuthId) {
    var user = validationUtils.isValidUser(teacherAuthId);
    var teacher = user.getTeacherInfo();
    var sections = sectionService.findByUuid(offlineTestDefinition.getSectionUuid());
    if (sections.getClassTeacher() != teacher) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Send.Notification",
          new String[] {user.getFirstName(), sections.getName()});
    }
    var isAllPublished =
        offlineTestDefinition.getOfflineTestScheduleSchedule().stream()
            .filter(ts -> isNull(ts.getDeletedAt()))
            .allMatch(ots -> Objects.nonNull(ots.getPublishedAt()));

    if (!isAllPublished) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Unable to send notification  without  publish the test");
    }
    if (Boolean.TRUE.equals(offlineTestDefinition.getNotificationStatus())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Already Notification sent to " + offlineTestDefinition.getTitle());
    }
  }

  public OfflineTestScheduleDto.AttendanceAndRemarksResponse getAttendance(
      Long offlineTestDefinitionId) {
    OfflineTestDefinition offlineTestDefinition =
        validateOfflineTestDefinition(offlineTestDefinitionId);
    List<OfflineTestScheduleStudentAttendance> offlineTestSchedule =
        offlineTestDefinition.getOfflineTestScheduleStudentAttendanceAndRemarks();

    return buildAttendanceAndRemarksResponseDto(offlineTestDefinition, offlineTestSchedule);
  }

  private OfflineTestScheduleDto.AttendanceAndRemarksResponse buildAttendanceAndRemarksResponseDto(
      OfflineTestDefinition offlineTestDefinition,
      List<OfflineTestScheduleStudentAttendance> studentsList) {
    return OfflineTestScheduleDto.AttendanceAndRemarksResponse.builder()
        .title(offlineTestDefinition.getTitle())
        .boardName(offlineTestDefinition.getBoardName())
        .boardSlug(offlineTestDefinition.getBoardSlug())
        .gradeName(offlineTestDefinition.getGradeName())
        .gradeSlug(offlineTestDefinition.getGradeSlug())
        .sectionName(validateSection(offlineTestDefinition.getSectionUuid()).getName())
        .sectionUuid(offlineTestDefinition.getSectionUuid())
        .totalAttendanceDays(offlineTestDefinition.getTotalAttendanceDays())
        .studentsData(buildStudentsData(studentsList))
        .build();
  }

  private List<OfflineTestScheduleDto.StudentsData> buildStudentsData(
      List<OfflineTestScheduleStudentAttendance> studentsList) {
    List<OfflineTestScheduleDto.StudentsData> response = new ArrayList<>();
    studentsList.forEach(
        student -> {
          var studentDetails = validationUtils.isStudentValid(student.getStudentId());
          var userDetails = studentDetails.getUserInfo();
          response.add(buildStudentDataDto(student, studentDetails, userDetails));
        });
    return response;
  }

  private OfflineTestScheduleDto.StudentsData buildStudentDataDto(
      OfflineTestScheduleStudentAttendance student, Student studentDetails, User userDetails) {
    return OfflineTestScheduleDto.StudentsData.builder()
        .remarks(student.getRemarks())
        .totalPresentDays(student.getPresentDays())
        .rollNo(studentDetails.getRollNumber())
        .studentId(student.getStudentId())
        .name(userDetails.getFirstName() + " " + userDetails.getLastName())
        .build();
  }

  public void saveAttendance(
      Long offlineTestDefinitionId, OfflineTestScheduleDto.AttendanceAndRemarksRequest request) {
    OfflineTestDefinition offlineTestDefinition;
    offlineTestDefinition = validateOfflineTestDefinition(offlineTestDefinitionId);
    offlineTestDefinition.setTotalAttendanceDays(
        request.totalAttendance() == null ? null : request.totalAttendance().toString());
    offlineTestDefinitionRepository.save(offlineTestDefinition);
    List<OfflineTestScheduleStudentAttendance> studentData;
    studentData = offlineTestDefinition.getOfflineTestScheduleStudentAttendanceAndRemarks();

    Map<Long, OfflineTestScheduleStudentAttendance> studentMap =
        studentData.stream()
            .collect(
                Collectors.toMap(
                    OfflineTestScheduleStudentAttendance::getStudentId, Function.identity()));

    request
        .attendance()
        .forEach(
            attendance ->
                studentMap.compute(
                    attendance.studentId(),
                    (id, data) -> {
                      if (data == null) {

                        data = new OfflineTestScheduleStudentAttendance();
                        data.setStudentId(attendance.studentId());
                        data.setOfflineTestDefinition(offlineTestDefinition);
                      }
                      data.setPresentDays(attendance.presentDays());
                      data.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
                      return data;
                    }));

    if (request.remarks() != null) {
      request
          .remarks()
          .forEach(
              remark ->
                  studentMap.compute(
                      remark.studentId(),
                      (id, data) -> {
                        if (data == null) {

                          data = new OfflineTestScheduleStudentAttendance();
                          data.setStudentId(remark.studentId());
                          data.setOfflineTestDefinition(offlineTestDefinition);
                        }
                        data.setRemarks(remark.remarks());
                        data.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
                        return data;
                      }));
    }

    offlineTestScheduleStudentAttendanceAndRemarksRepository.saveAll(studentMap.values());
  }

  public List<OfflineTestScheduleDto.ReportCardResponse> reportCard(
      List<Long> offlineTestDefinitionId,
      String boardSlug,
      String gradeSlug,
      String sectionUuid,
      ReportCardTemplateType type,
      Long reportCardTemplate,
      String orgSlug) {
    if (type == null) {
      return Collections.emptyList();
    }

    switch (type) {
      case CANNED -> {
        if (offlineTestDefinitionId == null) {
          throw new ApiException(
              InternalErrorCodes.INVALID_REQUEST, "Test DefinitionId is required for Canned type");
        }
        return generateReportCardForCanned(offlineTestDefinitionId);
      }
      case CUSTOM -> {
        if (boardSlug == null || gradeSlug == null) {
          throw new ApiException(
              InternalErrorCodes.INVALID_REQUEST, "Board & Grade are required for Custom type");
        }
        return generateReportCardForCustom(
            boardSlug, gradeSlug, sectionUuid, latestAcademicYear, orgSlug, reportCardTemplate);
      }
      default -> {
        return Collections.emptyList();
      }
    }
  }

  private List<OfflineTestScheduleDto.ReportCardResponse> generateReportCardForCanned(
      List<Long> offlineTestDefinitionId) {
    List<OfflineTestSchedule> testSchedules = new ArrayList<>();
    List<Long> offlineTestScheduleIds = new ArrayList<>();
    long totalMarks = 0L;

    for (Long testDefinition : offlineTestDefinitionId) {
      OfflineTestDefinition offlineTestDefinition = validateOfflineTestDefinition(testDefinition);
      testSchedules.addAll(offlineTestDefinition.getOfflineTestScheduleSchedule());
      offlineTestScheduleIds.addAll(
          testSchedules.stream().map(OfflineTestSchedule::getId).toList());
      totalMarks += testSchedules.stream().mapToLong(OfflineTestSchedule::getMarks).sum();
    }

    if (testSchedules.isEmpty() || offlineTestScheduleIds.isEmpty()) {
      return Collections.emptyList();
    }

    return generateReportCard(offlineTestScheduleIds, totalMarks);
  }

  private List<OfflineTestScheduleDto.ReportCardResponse> generateReportCardForCustom(
      String boardSlug,
      String gradeSlug,
      String sectionUuid,
      String latestAcademicYear,
      String orgSlug,
      Long reportCardTemplateId) {

    var section = validateSection(sectionUuid);

    var termSlug =
        Objects.nonNull(reportCardTemplateId)
            ? getTermByTemplate(reportCardTemplateRepository.findById(reportCardTemplateId))
            : null;
    var testDefinitions =
        getTestDefs(orgSlug, boardSlug, gradeSlug, sectionUuid, latestAcademicYear, termSlug);

    if (orgSlug.equals(gillcoOrgSlug)
        && gradeSlugs.contains(gradeSlug)
        && testDefinitions.isEmpty()) {
      List<Student> students = studentRepository.getStudentsBySection(section);
      return generateCustomReportCardForGillco(students);
    }

    if (testDefinitions.isEmpty()) {
      return Collections.emptyList();
    }

    var offlineTestScheduleIds =
        testDefinitions.stream()
            .map(OfflineTestDefinition::getOfflineTestScheduleSchedule)
            .flatMap(Collection::stream)
            .map(OfflineTestSchedule::getId)
            .toList();

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            termSlug, section.getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    return generateCustomReportCard(offlineTestScheduleIds, termAssessmentIds);
  }

  private List<OfflineTestScheduleDto.ReportCardResponse> generateCustomReportCardForGillco(
      List<Student> students) {

    return students.stream()
        .map(
            student ->
                OfflineTestScheduleDto.ReportCardResponse.builder()
                    .studentId(student.getId())
                    .name(userService.getNameByUserInfo(student.getUserInfo()))
                    .authUserId(student.getUserInfo().getAuthUserId())
                    .percentage(null)
                    .build())
        .toList();
  }

  private List<String> getTermByTemplate(Optional<ReportCardTemplate> reportCardTemplate) {
    if (reportCardTemplate.isPresent()) {
      if (StringUtils.containsAny("term1", reportCardTemplate.get().getConfig())) {
        return List.of("t1");
      } else if (StringUtils.containsAny("term2", reportCardTemplate.get().getConfig())) {
        return List.of("t2");
      }
    }
    return List.of("t1", "t2", "d1");
  }

  private List<OfflineTestDefinition> getTestDefs(
      String orgSlug,
      String boardSlug,
      String gradeSlug,
      String sectionUuid,
      String latestAcademicYear,
      List<String> termSlug) {
    if (Objects.nonNull(termSlug) && Objects.nonNull(sectionUuid)) {
      return offlineTestDefinitionRepository.findBySectionUuidAndOrgSlugAndTermSlug(
          sectionUuid, orgSlug, termSlug.getFirst());
    }
    if (Objects.nonNull(sectionUuid)) {
      return offlineTestDefinitionRepository.findBySectionUuidAndOrgSlug(sectionUuid, orgSlug);
    }
    return offlineTestDefinitionRepository
        .findAllByBoardSlugAndGradeSlugAndAcademicYearSlugAndOrgSlug(
            boardSlug, gradeSlug, latestAcademicYear, orgSlug);
  }

  private List<OfflineTestScheduleDto.ReportCardResponse> generateCustomReportCard(
      List<Long> offlineTestScheduleIds, List<Long> assessmentIds) {
    var studentData =
        offlineTestScheduleRepository.getStudentMarksByOfflineTestScheduleId(
            offlineTestScheduleIds);
    var studentIds = studentData.stream().map(ReportCardStudentData::getStudentId).toList();

    var reportCardData =
        offlineTestScheduleRepository.getStudentReportByStudents(studentIds, assessmentIds);

    var reportCardDataMap =
        reportCardData.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getStudentId));

    return studentData.stream()
        .map(
            data ->
                OfflineTestScheduleDto.ReportCardResponse.builder()
                    .name(data.getName())
                    .studentId(data.getStudentId())
                    .authUserId(data.getAuthUserId())
                    .percentage(getStudentPercentage(reportCardDataMap.get(data.getStudentId())))
                    .build())
        .toList();
  }

  private Double getStudentPercentage(List<LowerGradeReportCardData> studentReportCard) {

    return Objects.nonNull(studentReportCard) && !studentReportCard.isEmpty()
        ? Double.parseDouble(
            Constants.DECIMAL_FORMAT.format(
                studentReportCard.stream().mapToDouble(LowerGradeReportCardData::getMarks).sum()
                    / studentReportCard.stream()
                        .mapToDouble(LowerGradeReportCardData::getTotalMarks)
                        .sum()
                    * 100))
        : null;
  }

  private List<OfflineTestScheduleDto.ReportCardResponse> generateReportCard(
      List<Long> offlineTestScheduleIds, long totalMarks) {
    var studentData =
        offlineTestScheduleRepository.getStudentMarksByOfflineTestScheduleId(
            offlineTestScheduleIds);

    return studentData.stream()
        .map(
            data ->
                OfflineTestScheduleDto.ReportCardResponse.builder()
                    .name(data.getName())
                    .studentId(data.getStudentId())
                    .authUserId(data.getAuthUserId())
                    .percentage(
                        data.getMarks() == null
                            ? 0.0
                            : Math.round((double) data.getMarks() / totalMarks * 100))
                    .build())
        .toList();
  }

  public void refreshOfflineTestScheduleStudents(Long offlineTestScheduleId) {
    OfflineTestSchedule offlineTestSchedule = validateOfflineTestSchedule(offlineTestScheduleId);
    var offlineTestDefinition = offlineTestSchedule.getOfflineTestDefinition();
    var section = validationUtils.findSectionByUuid(offlineTestDefinition.getSectionUuid());

    final List<OfflineTestScheduleStudent> latestOfflineTestScheduleStudents =
        buildOfflineTestScheduleStudents(section, offlineTestSchedule);
    // check two list and append extra items to the list
    final List<OfflineTestScheduleStudent> currentStudentList =
        offlineTestSchedule.getOfflineTestScheduleStudents();
    final List<OfflineTestScheduleStudent> newStudents = new ArrayList<>();
    final List<OfflineTestScheduleStudent> removedStudents = new ArrayList<>();
    for (OfflineTestScheduleStudent latestStudent : latestOfflineTestScheduleStudents) {
      if (currentStudentList.stream()
          .noneMatch(
              currentStudent ->
                  currentStudent.getStudentId().equals(latestStudent.getStudentId()))) {
        newStudents.add(latestStudent);
      }
    }

    // Remove all the current students in offline_test_schedule who are not in
    // latestOfflineTestScheduleStudents and whose marks are not entered
    for (OfflineTestScheduleStudent currentStudent : currentStudentList) {
      final boolean isCurrentStudentReadyForRemoval =
          latestOfflineTestScheduleStudents.stream()
              .filter(s -> s.getStudentId().equals(currentStudent.getStudentId()))
              .toList()
              .isEmpty();
      if (isCurrentStudentReadyForRemoval) {
        removedStudents.add(currentStudent);
      }
    }

    if (newStudents.isEmpty() && removedStudents.isEmpty()) {
      return;
    }

    if (!newStudents.isEmpty()) {
      currentStudentList.addAll(newStudents);
    }
    if (!removedStudents.isEmpty()) {
      currentStudentList.removeAll(removedStudents);
    }
    offlineTestSchedule.setOfflineTestScheduleStudents(currentStudentList);
    offlineTestScheduleRepository.save(offlineTestSchedule);
  }

  public OfflineTestScheduleDto.MarksListResponse getStudentsMarksListResponse(
      Long offlineTestDefinitionId, String sectionUuid) {
    var offlineTestDefinition =
        offlineTestDefinitionRepository
            .findById(offlineTestDefinitionId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.InvalidTestDefinition"));
    var section =
        sectionRepository
            .findByUuid(UUID.fromString(sectionUuid))
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound"));
    var studentMarks = buildStudentMarksList(offlineTestDefinition, section);
    return OfflineTestScheduleDto.MarksListResponse.builder()
        .offlineTestDefinitionId(offlineTestDefinition.getId())
        .title(offlineTestDefinition.getTitle())
        .academicYearSlug(offlineTestDefinition.getAcademicYearSlug())
        .boardSlug(offlineTestDefinition.getBoardSlug())
        .boardName(offlineTestDefinition.getBoardName())
        .gradeSlug(offlineTestDefinition.getGradeSlug())
        .gradeName(offlineTestDefinition.getGradeName())
        .sectionName(section.getName())
        .sectionUuid(section.getUuid().toString())
        .studentMarksLists(studentMarks)
        .overall(buildOverAllDetails(studentMarks))
        .build();
  }

  private List<OfflineTestScheduleDto.Overall> buildOverAllDetails(
      List<OfflineTestScheduleDto.StudentMarksList> studentMarks) {
    List<OfflineTestScheduleDto.Overall> overallList = new ArrayList<>();

    overallList.addAll(buildStudentMarks(studentMarks));
    overallList.add(buildStudentTotals(studentMarks));

    return overallList;
  }

  private OfflineTestScheduleDto.Overall buildStudentTotals(
      List<OfflineTestScheduleDto.StudentMarksList> studentMarks) {
    double totalMarks =
        studentMarks.stream()
            .map(OfflineTestScheduleDto.StudentMarksList::totalMarksResponse)
            .map(OfflineTestScheduleDto.TotalMarksResponse::totalMarks)
            .filter(Objects::nonNull)
            .mapToDouble(BigDecimal::doubleValue)
            .sum();

    var count =
        studentMarks.stream()
            .map(OfflineTestScheduleDto.StudentMarksList::totalMarksResponse)
            .count();

    var percentage = (count > 0) ? (totalMarks / count) : 0.0;

    return OfflineTestScheduleDto.Overall.builder()
        .offlineTestScheduleId(null)
        .title("overall")
        .marks(formatMarks(percentage))
        .build();
  }

  private List<OfflineTestScheduleDto.Overall> buildStudentMarks(
      List<OfflineTestScheduleDto.StudentMarksList> studentMarks) {
    List<OfflineTestScheduleDto.Overall> overallList = new ArrayList<>();
    var subjects =
        studentMarks.stream()
            .map(OfflineTestScheduleDto.StudentMarksList::offlineTestScheduleResponse)
            .flatMap(List::stream)
            .toList();

    var uniqueScheduleIds =
        subjects.stream()
            .map(OfflineTestScheduleDto.OfflineTestScheduleResponse::offlineTestScheduleId)
            .distinct()
            .toList();

    uniqueScheduleIds.forEach(
        scheduleId -> {
          double totalMarks =
              studentMarks.stream()
                  .flatMap(studentMark -> studentMark.offlineTestScheduleResponse().stream())
                  .filter(response -> response.offlineTestScheduleId().equals(scheduleId))
                  .mapToDouble(response -> parseMarks(response.marks()))
                  .sum();

          long count =
              studentMarks.stream()
                  .flatMap(studentMark -> studentMark.offlineTestScheduleResponse().stream())
                  .filter(response -> response.offlineTestScheduleId().equals(scheduleId))
                  .count();

          String title =
              subjects.stream()
                  .filter(response -> response.offlineTestScheduleId().equals(scheduleId))
                  .map(OfflineTestScheduleDto.OfflineTestScheduleResponse::title)
                  .findFirst()
                  .orElse("Unknown Title");
          double percentage = (count > 0) ? (totalMarks / count) : 0.0;

          overallList.add(
              OfflineTestScheduleDto.Overall.builder()
                  .offlineTestScheduleId(scheduleId)
                  .title(title)
                  .marks(formatMarks(percentage))
                  .build());
        });

    return overallList;
  }

  public double formatMarks(double percentage) {
    DecimalFormat decimalFormat = new DecimalFormat("0.0");
    return Double.parseDouble(decimalFormat.format(percentage));
  }

  private double parseMarks(Object marks) {
    if (marks == null) {
      return 0.0;
    }
    try {
      if (marks instanceof BigDecimal bigDecimal) {
        return bigDecimal.doubleValue();
      } else if (marks instanceof String str) {
        return Double.parseDouble(str);
      } else if (marks instanceof Number number) {
        return number.doubleValue();
      } else {
        return 0.0;
      }
    } catch (NumberFormatException e) {
      return 0.0;
    }
  }

  private List<OfflineTestScheduleDto.StudentMarksList> buildStudentMarksList(
      OfflineTestDefinition offlineTestDefinition, Section section) {
    var sectionStudents = studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
    var activeStudents =
        sectionStudents.stream()
            .filter(student -> student.getUserInfo().getDeletedAt() == null)
            .toList();
    List<OfflineTestSchedule> offlineTestSchedules =
        offlineTestDefinition.getOfflineTestScheduleSchedule().stream()
            .filter(
                offlineTestSchedule ->
                    offlineTestSchedule.getDeletedAt() == null
                        && Boolean.TRUE.equals(offlineTestSchedule.getShowRc()))
            .toList();

    Set<OfflineTestScheduleStudent> offlineTestScheduleStudents = new HashSet<>();
    offlineTestSchedules.forEach(
        offlineTestSchedule ->
            offlineTestScheduleStudents.addAll(
                offlineTestSchedule.getOfflineTestScheduleStudents()));

    List<OfflineTestScheduleDto.StudentMarksList> response = new ArrayList<>();
    for (Student student : activeStudents) {
      List<OfflineTestScheduleStudent> studentOfflineTestSchedules =
          offlineTestScheduleStudents.stream()
              .filter(test -> test.getStudentId().equals(student.getId()))
              .filter(
                  test ->
                      test.getOfflineTestScheduleDetails()
                          .getConsiderPercentage()
                          .equals(Boolean.TRUE))
              .toList();
      response.add(
          OfflineTestScheduleDto.StudentMarksList.builder()
              .studentId(String.valueOf(student.getId()))
              .studentAuthId(student.getUserInfo().getAuthUserId())
              .studentName(userService.getNameByUserInfo(student.getUserInfo()))
              .studentAdmissionNumber(student.getRollNumber())
              .studentClassRollNumber(student.getClassRollNumber())
              .offlineTestScheduleResponse(
                  buildStudentOfflineTestScheduleDetails(student, offlineTestSchedules))
              .totalMarksResponse(buildTotalMaksAndGrade(studentOfflineTestSchedules))
              .build());
    }
    return response.stream()
        .sorted(
            Comparator.comparing(
                student ->
                    StringUtils.isNumeric(student.studentClassRollNumber())
                        ? Long.valueOf(student.studentClassRollNumber())
                        : null,
                Comparator.nullsLast(Comparator.naturalOrder())))
        .toList();
  }

  private OfflineTestScheduleDto.TotalMarksResponse buildTotalMaksAndGrade(
      List<OfflineTestScheduleStudent> offlineTestScheduleStudents) {
    if (offlineTestScheduleStudents.isEmpty()) {
      return OfflineTestScheduleDto.TotalMarksResponse.builder()
          .totalMarks(BigDecimal.valueOf(0))
          .overallGrade(null)
          .build();
    }

    List<OfflineTestSchedule> studentOfflineTests = new ArrayList<>();
    offlineTestScheduleStudents.forEach(
        studentOfflineTestSchedule ->
            studentOfflineTests.add(studentOfflineTestSchedule.getOfflineTestScheduleDetails()));
    var testsTotalMarks =
        studentOfflineTests.stream()
            .filter(
                offlineTestSchedule ->
                    offlineTestSchedule.getConsiderPercentage().equals(Boolean.TRUE))
            .mapToLong(OfflineTestSchedule::getMarks)
            .sum();

    var studentTotalMarks =
        offlineTestScheduleStudents.stream()
            .filter(test -> test.getMarks() != null)
            .mapToDouble(test -> test.getMarks().doubleValue())
            .sum();

    var grade =
        pointScaleEvaluator.evaluate(
            offlineTestScheduleStudents
                .getFirst()
                .getOfflineTestScheduleDetails()
                .getOfflineTestDefinition()
                .getGradeScaleSlug(),
            BigDecimal.valueOf((studentTotalMarks / testsTotalMarks) * 100));
    return OfflineTestScheduleDto.TotalMarksResponse.builder()
        .totalMarks(BigDecimal.valueOf(studentTotalMarks))
        .overallGrade(grade)
        .build();
  }

  private List<OfflineTestScheduleDto.OfflineTestScheduleResponse>
      buildStudentOfflineTestScheduleDetails(
          Student student, List<OfflineTestSchedule> offlineTestSchedules) {

    List<OfflineTestSchedule> sortedTests =
        offlineTestSchedules.stream()
            .sorted(
                Comparator.comparingInt(
                    offlineTestSchedule ->
                        Math.toIntExact(offlineTestSchedule.getSubjectsMetaData().getSeqNo())))
            .toList();

    List<OfflineTestScheduleDto.OfflineTestScheduleResponse> responses = new ArrayList<>();
    for (OfflineTestSchedule offlineTestSchedule : sortedTests) {
      List<OfflineTestScheduleStudent> offlineTestScheduleStudents =
          offlineTestSchedule.getOfflineTestScheduleStudents();
      List<OfflineTestScheduleStudent> studentOfflineTestSchedule =
          offlineTestScheduleStudents.stream()
              .filter(
                  offlineTestScheduleStudent ->
                      offlineTestScheduleStudent.getStudentId().equals(student.getId()))
              .toList();
      if (!studentOfflineTestSchedule.isEmpty()) {
        var grade =
            studentOfflineTestSchedule.getFirst().getMarks() == null
                ? null
                : pointScaleEvaluator.evaluate(
                    offlineTestSchedule.getOfflineTestDefinition().getGradeScaleSlug(),
                    BigDecimal.valueOf(
                        (studentOfflineTestSchedule.getFirst().getMarks().doubleValue()
                                / offlineTestSchedule.getMarks().doubleValue())
                            * 100));
        Object marks;
        if (studentOfflineTestSchedule.getFirst().getIsAttended() == null
            || studentOfflineTestSchedule.getFirst().getIsAttended().equals(Boolean.TRUE)) {
          marks = studentOfflineTestSchedule.getFirst().getMarks();
        } else {
          marks = studentOfflineTestSchedule.getFirst().getRemarks();
        }
        responses.add(
            OfflineTestScheduleDto.OfflineTestScheduleResponse.builder()
                .offlineTestScheduleId(offlineTestSchedule.getId())
                .isReportCard(offlineTestSchedule.getShowRc())
                .isPercentage(offlineTestSchedule.getConsiderPercentage())
                .title(
                    offlineTestSchedule.getSubjectsMetaData().getName()
                        + "("
                        + offlineTestSchedule.getMarks()
                        + ")")
                .marks(marks)
                .grade(grade)
                .build());
      } else {
        responses.add(
            OfflineTestScheduleDto.OfflineTestScheduleResponse.builder()
                .offlineTestScheduleId(offlineTestSchedule.getId())
                .isReportCard(offlineTestSchedule.getShowRc())
                .isPercentage(offlineTestSchedule.getConsiderPercentage())
                .title(
                    offlineTestSchedule.getSubjectsMetaData().getName()
                        + "("
                        + offlineTestSchedule.getMarks()
                        + ")")
                .marks(null)
                .grade(null)
                .build());
      }
    }
    return responses;
  }

  public void migrateOfflineDefinition(String orgSlug) {
    var org = validationUtils.isOrgValid(orgSlug);
    var offlineTestDefinitions = offlineTestDefinitionRepository.getAllByOrgSlug(orgSlug);

    if (offlineTestDefinitions.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "No OfflineTests are available for the organization: " + org.getName());
    }
    offlineTestDefinitions.forEach(
        offlineTestDefinition -> {
          var testSchedules = offlineTestDefinition.getOfflineTestScheduleSchedule();
          if (!testSchedules.isEmpty()) {
            testSchedules.stream()
                .filter(
                    testSchedule ->
                        Objects.isNull(testSchedule.getSubjectsMetaData())
                            && Objects.nonNull(testSchedule.getSubjectName()))
                .forEach(
                    testSchedule -> {
                      var subjectMetaData =
                          subjectsMetaDataRepository.findByOrgSlugAndNameAndGradeSlugAndBoardSlug(
                              orgSlug,
                              testSchedule.getSubjectName().toUpperCase(),
                              offlineTestDefinition.getGradeSlug(),
                              offlineTestDefinition.getBoardSlug());
                      subjectMetaData.ifPresent(testSchedule::setSubjectsMetaData);
                    });
            offlineTestDefinitionRepository.save(offlineTestDefinition);
          }
        });
  }

  @Async
  @Transactional
  public void migrateAssociateTestScheduleStudent(Student student) {
    var scheduleStudents = offlineTestScheduleStudentRepository.findByStudentId(student.getId());
    var offlineTestDefinitions =
        offlineTestDefinitionRepository.findBySectionUuidAndOrgSlug(
            student.getSection().getUuid().toString(), student.getUserInfo().getOrganization());
    if (offlineTestDefinitions.isEmpty() || scheduleStudents.isEmpty()) {
      return;
    }
    try {
      var offlineTestSchedules =
          offlineTestDefinitions.stream()
              .map(OfflineTestDefinition::getOfflineTestScheduleSchedule)
              .flatMap(Collection::stream)
              .toList();

      Map<SubjectsMetaData, List<OfflineTestSchedule>> offlineTestScheduleMap =
          offlineTestSchedules.stream()
              .collect(Collectors.groupingBy(OfflineTestSchedule::getSubjectsMetaData));

      scheduleStudents.stream()
          .filter(s -> Objects.nonNull(s.getOfflineTestScheduleDetails()))
          .forEach(
              scheduleStudent -> {
                var offlineTestScheduleList =
                    offlineTestScheduleMap.get(
                        scheduleStudent.getOfflineTestScheduleDetails().getSubjectsMetaData());

                var offlineTestSchedule =
                    offlineTestScheduleList.stream()
                        .filter(
                            ots ->
                                ots.getOfflineTestDefinition()
                                    .getTitle()
                                    .equals(
                                        scheduleStudent
                                            .getOfflineTestScheduleDetails()
                                            .getOfflineTestDefinition()
                                            .getTitle()))
                        .findAny();

                if (offlineTestSchedule.isPresent()) {
                  scheduleStudent.setOfflineTestScheduleDetails(offlineTestSchedule.get());
                  scheduleStudent.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
                }
              });
      offlineTestScheduleStudentRepository.saveAll(scheduleStudents);
    } catch (Exception e) {
      log.error("Error while migrating  offline test schedule of student " + student.getId(), e);
    }
  }

  public void migrateAssessmentCategoryInOfflineTestDef(String orgSlug) {
    List<OfflineTestDefinition> allOfflineTestDefinitions =
        offlineTestDefinitionRepository.findAllByOrgSlugAndDeletedAtIsNull(orgSlug);
    List<TermAssessment> termAssessments =
        allOfflineTestDefinitions.stream()
            .map(OfflineTestDefinition::getAssessment)
            .distinct()
            .toList();
    for (TermAssessment assessment : termAssessments) {
      var assessmentCategory =
          termAssessmentCategoryRepository
              .findAllByOrgSlugAndTermAssessmentAndName(
                  orgSlug, assessment, DEFAULT_ASSESSMENT_CATEGORY)
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "error.TermAssessmentCategory"));

      var offlineTestDefinitions =
          allOfflineTestDefinitions.stream()
              .filter(otd -> assessment.equals(otd.getAssessment()))
              .toList();
      List<OfflineTestDefinition> offlineTestDefinitionList = new ArrayList<>();
      for (OfflineTestDefinition otd : offlineTestDefinitions) {
        otd.setAssessmentCategory(assessmentCategory);
        offlineTestDefinitionList.add(otd);
      }
      offlineTestDefinitionRepository.saveAll(offlineTestDefinitionList);
      var otdIds = offlineTestDefinitions.stream().map(OfflineTestDefinition::getId).toList();
      var offlineTestScheduleStudents =
          offlineTestScheduleStudentRepository.getAllByOfflineTestDefinition(otdIds);
      List<OfflineTestScheduleStudent> offlineTestScheduleStudentList = new ArrayList<>();
      for (OfflineTestScheduleStudent otss : offlineTestScheduleStudents) {
        otss.setTermAssessmentCategory(assessmentCategory);
        offlineTestScheduleStudentList.add(otss);
      }

      offlineTestScheduleStudentRepository.saveAll(offlineTestScheduleStudentList);
    }
  }

  public void updateOfflineTestDefinition(
      String orgSlug, Long testDefinitionId, OfflineTestScheduleDto.Request request) {
    var offlineTestDefinition =
        offlineTestDefinitionRepository
            .findByIdAndOrgSlug(testDefinitionId, orgSlug)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Exam Schedule not found"));
    offlineTestDefinition.setShowStudents(request.showStudents());
    offlineTestDefinition.setShowAdmitCard(request.showAdmitCard());
    offlineTestDefinition.setReportCardTemplate(
        Objects.nonNull(request.reportCardTemplate())
            ? reportCardTemplateRepository
                .findByIdAndOrgSlug(request.reportCardTemplate(), orgSlug)
                .orElseThrow()
            : null);

    offlineTestDefinition.setTitle(request.title());
    offlineTestDefinition.setGradeScaleSlug(request.gradeScaleSlug());
    offlineTestDefinition.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    offlineTestDefinitionRepository.save(offlineTestDefinition);
  }

  public void fillMarksByScheduleTest(Long offlineTestScheduleId, Long testScheduleId) {
    try {
      var offlineTestSchedule = validateOfflineTestSchedule(offlineTestScheduleId);
      var scheduleTest = validationUtils.isTestScheduleValid(testScheduleId);
      var studentUsers =
          scheduleTest.getScheduleTestStudent().stream()
              .map(ScheduleTestStudent::getStudent)
              .toList();
      var students = studentRepository.findByUserInfoInAndDeletedAtIsNull(studentUsers);
      var exams = examRepository.findByScheduleTestAndStudentIn(scheduleTest, students);
      var examMap =
          exams.stream()
              .collect(Collectors.toMap(exam -> exam.getStudent().getId(), Function.identity()));
      offlineTestSchedule
          .getOfflineTestScheduleStudents()
          .forEach(
              otss -> {
                var exam = examMap.get(otss.getStudentId());
                if (Objects.nonNull(exam)) {
                  otss.setMarks(BigDecimal.valueOf(exam.getMarksScored()));
                  otss.setIsAttended(true);
                  otss.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
                }
              });
      offlineTestScheduleRepository.save(offlineTestSchedule);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Failed to fill marks :%s".formatted(e.getMessage()),
          e);
    }
  }

  public ReportCardDto.SectionDetails getSectionMarksEntry(String orgSlug, Long testScheduleId) {
    if (scholarsOrgSlugs.contains(orgSlug)) {
      var offlineTestSchedule =
          offlineTestScheduleRepository
              .findById(testScheduleId)
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "error.testScheduleNotFound"));

      var offlineTestDefinition = offlineTestSchedule.getOfflineTestDefinition();
      ReportCardDto.OrgDetailsResponse sectionMarksJson = getSectionMarksJson();

      var sectionDetails =
          sectionMarksJson.sectionDetails().stream()
              .filter(
                  detail ->
                      detail
                              .termAssessmentSlug()
                              .equals(offlineTestDefinition.getAssessment().getSlug())
                          && detail.grade().equals(offlineTestDefinition.getGradeSlug())
                          && detail
                              .subject()
                              .equals(
                                  offlineTestSchedule.getSubjectsMetaData().getWexlSubjectSlug()))
              .findFirst();
      if (sectionDetails.isEmpty()) {
        return null;
      }
      var sectionDetail = sectionDetails.get();
      List<ReportCardDto.Section> sectionMarks = new ArrayList<>();
      sectionDetail
          .sections()
          .forEach(
              section ->
                  sectionMarks.add(
                      ReportCardDto.Section.builder()
                          .name(section.name())
                          .marks(section.marks())
                          .build()));

      return ReportCardDto.SectionDetails.builder()
          .termAssessmentSlug(sectionDetail.termAssessmentSlug())
          .grade(sectionDetail.grade())
          .subject(sectionDetail.subject())
          .totalSections(sectionDetail.totalSections())
          .sections(sectionMarks)
          .build();
    }
    return null;
  }

  public ReportCardDto.OrgDetailsResponse getSectionMarksJson() {
    ObjectMapper objectMapper = new ObjectMapper();
    if (sectionMarksResource == null) {
      return null;
    }
    try {
      return objectMapper.readValue(
          sectionMarksResource.getInputStream(), ReportCardDto.OrgDetailsResponse.class);
    } catch (IOException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.filedToGetJson");
    }
  }
}
