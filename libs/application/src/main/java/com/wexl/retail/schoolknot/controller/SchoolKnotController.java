package com.wexl.retail.schoolknot.controller;

import com.wexl.retail.schoolknot.dto.SchoolKnotDto;
import com.wexl.retail.schoolknot.service.SchoolKnotService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
public class SchoolKnotController {
  private final SchoolKnotService schoolKnotService;

  @PostMapping("/erps/schoolknot/students")
  public void createStudent(@RequestBody SchoolKnotDto.schoolKnotStudentRequest request) {
    log.info("Received request to create student in school knot");
    schoolKnotService.createStudent(request);
  }

  @PostMapping("/erps/schoolknot/teachers")
  public void createTeacher(@RequestBody SchoolKnotDto.schoolKnotTeacherRequest request) {
    log.info("Received request to create teacher in school knot");
    schoolKnotService.createTeacher(request);
  }

  @PostMapping("/erps/schoolknot/sections")
  public void createSection(@RequestBody SchoolKnotDto.schoolKnotSectionRequest request) {
    log.info("Received request to create teacher in school knot");
    schoolKnotService.createSection(request);
  }
}
