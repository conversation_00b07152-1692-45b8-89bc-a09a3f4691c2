package com.wexl.retail.offlinetest.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import java.util.List;
import java.util.Map;
import lombok.Builder;

public record ReportCard() {

  @Builder
  public record Header(
      String schoolName,
      String schoolSubTitle,
      String academicYear,
      String studentName,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String dateOfBirth,
      String admissionNumber) {}

  @Builder
  public record Marks(
      String sno,
      String subject,
      String internalAssessment,
      String annualExam,
      String total,
      String grade) {}

  @Builder
  public record Card(
      Header header,
      String scholasticScale,
      String coScholasticScale,
      int marksColumnCount,
      String marksColumnTitle1,
      String marksColumnTitle2,
      String marksColumnTitle3,
      String totalMarksTitle,
      List<CoScholasticMarks> coScholasticMarks,
      List<ScholasticMarks> scholasticMarks,
      List<AdditionalMarks> additionalMarks,
      Attendance attendance,
      String teacherRemarks) {}

  @Builder
  public record CoScholasticMarks(String sno, String subject, String grade) {}

  @Builder
  public record AdditionalMarks(String sno, String subject, String grade) {}

  public record ScholasticMarks(
      String sno,
      String subject,
      String marks1,
      String marks2,
      String marks3,
      String total,
      String grade) {}

  public record Attendance(String totalDays, String daysPresent, String percentage) {}

  @Builder
  public record ReportCardResponse(
      @JsonProperty("report_card_template_id") Long reportCardTemplateId,
      @JsonProperty("report_card_template_Name") String reportCardTemplateName,
      @JsonProperty("report_card_template_type") ReportCardTemplateType reportCardTemplateType,
      @JsonProperty("report_card_config_id") Long reportCardConfigId,
      @JsonProperty("test_definition_name") String testDefinitionName,
      @JsonProperty("test_definition_id") Long testDefinitionId,
      @JsonProperty("term_name") String termName,
      @JsonProperty("term_slug") String termSlug,
      @JsonProperty("term_id") Long termId,
      @JsonProperty("subject_metadata_id") Long subjectMetadataId,
      @JsonProperty("subject_metadata_name") String subjectMetadataName,
      @JsonProperty("subject_metadata_type") String subjectMetadataType) {}

  @Builder
  public record StudentReportCards(List<ReportCardResponse> reportCardResponses) {}

  @Builder
  public record StudentMarksRequest(
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("section_uuid") String sectionUuid) {}

  @Builder
  public record StudentMarksResponse(
      @JsonProperty("academic_year") String academicYear,
      String campus,
      String curriculum,
      String className,
      String section,
      String term,
      @JsonProperty("student_list") List<StudentsList> studentsList) {}

  @Builder
  public record StudentsList(
      @JsonProperty("roll_number") Long rollNumber,
      @JsonProperty("class_roll_no") Long classRollNumber,
      @JsonProperty("student_id") Long studentId,
      String name,
      List<Map<String, Object>> subjects) {}
}
