package com.wexl.retail.appreciation.controller.controller;

import com.wexl.retail.appreciation.controller.service.AnnouncementService;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController("sectionAnnouncementController")
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgId}/sections/{sectionId}/announcements")
public class SectionAnnouncementController {

  private final AnnouncementService announcementService;

  @PostMapping("/attachments")
  @PreAuthorize("hasRole('ROLE_TEACHER') or hasRole('ROLE_ITEACHER')")
  public List<S3FileUploadResult> uploadAttachments(
      @PathVariable("orgId") String orgSlug,
      @PathVariable("sectionId") String sectionUuid,
      @RequestPart(value = "files") List<MultipartFile> multipartFiles) {
    return announcementService.uploadAnnouncementAttachments(sectionUuid, orgSlug, multipartFiles);
  }
}
