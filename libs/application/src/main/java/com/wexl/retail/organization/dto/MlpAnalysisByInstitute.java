package com.wexl.retail.organization.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class MlpAnalysisByInstitute {
  private String nameOrgs;
  private Long usersCount;
  private Long teachersCount;
  private Long mlpsAttendence;
  private Long mlpsTriggered;
}
