package com.wexl.retail.appreciation.controller.service;

import static com.wexl.retail.util.Constants.BACK_SLASH;

import com.wexl.retail.appreciation.controller.domail.DomainType;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.storage.StorageService;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class AnnouncementService {

  private final StorageService storageService;

  @SneakyThrows
  public List<S3FileUploadResult> uploadAnnouncementAttachments(
      String sectionUuid, String orgSlug, List<MultipartFile> multipartFiles) {

    List<S3FileUploadResult> uploadResults = new ArrayList<>();

    for (MultipartFile multipartFile : multipartFiles) {
      String fileName = multipartFile.getOriginalFilename();
      String filePath = constructAnnouncementAttachmentFilePath(sectionUuid, orgSlug, fileName);
      storageService.uploadFile(multipartFile, filePath);
      uploadResults.add(
          S3FileUploadResult.builder()
              .path(filePath)
              .url(storageService.generatePreSignedUrlForFetch(filePath))
              .build());
    }
    return uploadResults;
  }

  private String constructAnnouncementAttachmentFilePath(
      String sectionUuid, String orgSlug, String fileName) {
    return orgSlug
        .concat(BACK_SLASH + sectionUuid)
        .concat(BACK_SLASH + DomainType.ANNOUNCEMENT)
        .concat(BACK_SLASH + LocalDate.now())
        .concat(BACK_SLASH + fileName);
  }
}
