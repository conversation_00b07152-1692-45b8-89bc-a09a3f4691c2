package com.wexl.retail.offlinetest.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;

public record OfflineTestScheduleDto() {
  @Builder
  public record Request(
      @JsonProperty("title") String title,
      @JsonProperty("academic_year_slug") String academicYearSlug,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_uuid") List<String> sectionUuid,
      @JsonProperty("exam_name") String examName,
      @JsonProperty("exam_start_date") Long examStartDate,
      @JsonProperty("exam_end_date") Long examEndDate,
      @JsonProperty("term_slug") String termSlug,
      @JsonProperty("assessment_id") Long assessmentId,
      @JsonProperty("assessment_category_id") Long assessmentCategoryId,
      @JsonProperty("grade_scale_slug") String gradeScaleSlug,
      @JsonProperty("offline_test_definition_id") Long offlineTestDefinitionId,
      @JsonProperty("report_card_template") Long reportCardTemplate,
      @JsonProperty("show_students") Boolean showStudents,
      @JsonProperty("show_admit_card") Boolean showAdmitCard,
      @JsonProperty("subjects") List<Subjects> subjects) {}

  @Builder
  public record Subjects(
      @JsonProperty("offline_test_schedule_id") Long id,
      @JsonProperty("subject_metadata_id") Long subjectMetaDataId,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("marks") Long marks,
      @JsonProperty("date") Long date,
      @JsonProperty("exam_start_time") Long examStartTime,
      @JsonProperty("exam_end_time") Long examEndTime,
      @JsonProperty("published_at") Long publishedAt,
      @JsonProperty("is_report_card") Boolean isReportCard,
      @JsonProperty("is_percentage") Boolean isPercentage) {}

  @Builder
  public record Response(
      @JsonProperty("offline_test_schedule_id") Long offlineTestScheduleId,
      @JsonProperty("title") String title,
      @JsonProperty("academic_year_slug") String academicYearSlug,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("type") SubjectsTypeEnum type,
      @JsonProperty("category") SubjectsCategoryEnum subjectsCategoryEnum,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("exam_name") String examName,
      @JsonProperty("exam_start_date") Long examStartDate,
      @JsonProperty("exam_end_date") Long examEndDate,
      @JsonProperty("total_marks") Long totalMarks,
      @JsonProperty("term_slug") String termSlug,
      @JsonProperty("assessment_slug") String assessmentSlug,
      @JsonProperty("assessment_category_id") Long assessmentCategoryId,
      @JsonProperty("grade_scale_slug") String gradeScaleSlug,
      @JsonProperty("offline_test_definition_id") Long offlineTestDefinitionId,
      @JsonProperty("subjects") List<Subjects> subjects,
      @JsonProperty("student_details") List<StudentsResponse> studentsDetails,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("notification_status") boolean notificationStatus,
      @JsonProperty("report_card_template_id") Long reportCardTemplateId,
      @JsonProperty("report_card_template_name") String reportCardTemplateName,
      @JsonProperty("show_students") Boolean showStudents,
      @JsonProperty("show_admit_card") Boolean showAdmitCard) {}

  @Builder
  public record StudentsResponse(
      @JsonProperty("student_Id") Long studentId,
      @JsonProperty("name") String name,
      @JsonProperty("roll_number") String classRollNumber,
      @JsonProperty("admission_number") String admissionNumber,
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("student_authId") String student_auth_id,
      @JsonProperty("marks") String marks,
      @JsonProperty("result") String result,
      @JsonProperty("marks_1") String marks1,
      @JsonProperty("marks_1grade") String marks1Grade,
      @JsonProperty("marks_1_display") String marks1Display,
      @JsonProperty("marks_2") String marks2,
      @JsonProperty("marks_2_display") String marks2Display,
      @JsonProperty("marks_3") String marks3,
      @JsonProperty("marks_3_display") String marks3Display,
      @JsonProperty("remarks") String remarks,
      @JsonProperty("is_attended") Boolean isAttended,
      @JsonProperty("section_wise_marks") List<SectionWiseMarks> sectionWiseMarks) {}

  @Builder
  public record StudentsRequest(@JsonProperty("students") List<Students> students) {}

  @Builder
  public record Students(
      @JsonProperty("student_Id") Long studentId,
      @JsonProperty("marks") BigDecimal marks,
      @JsonProperty("marks_1") BigDecimal marks1,
      @JsonProperty("marks_1grade") String marks1Grade,
      @JsonProperty("marks_2") BigDecimal marks2,
      @JsonProperty("marks_3") BigDecimal marks3,
      @JsonProperty("is_attended") Boolean isAttended,
      @JsonProperty("remarks") String remarks,
      @JsonProperty("section_wise_marks") List<SectionWiseMarks> sectionWiseMarks) {}

  @Builder
  public record SectionWiseMarks(
      @JsonProperty("section") String section,
      @JsonProperty("total_marks") BigDecimal totalMarks,
      @JsonProperty("marks") BigDecimal marks) {}

  @Builder
  public record AttendanceAndRemarksResponse(
      @JsonProperty("title") String title,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("total_attendance_days") String totalAttendanceDays,
      @JsonProperty("students_data") List<StudentsData> studentsData) {}

  @Builder
  public record StudentsData(
      @JsonProperty("name") String name,
      @JsonProperty("roll_no") String rollNo,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("total_present_days") Long totalPresentDays,
      @JsonProperty("remarks") String remarks) {}

  @Builder
  public record AttendanceAndRemarksRequest(
      @JsonProperty("total_attendance") Long totalAttendance,
      @JsonProperty("attendance") List<Attendance> attendance,
      @JsonProperty("remarks") List<Remarks> remarks) {}

  @Builder
  public record Attendance(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("total_present_days") Long presentDays) {}

  @Builder
  public record Remarks(
      @JsonProperty("student_id") Long studentId, @JsonProperty("remarks") String remarks) {}

  @Builder
  public record ReportCardResponse(
      @JsonProperty("name") String name,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("auth_user_id") String authUserId,
      @JsonProperty("percentage") Double percentage) {}

  @Builder
  public record MarksListResponse(
      @JsonProperty("offline_test_definition_id") Long offlineTestDefinitionId,
      @JsonProperty("title") String title,
      @JsonProperty("academic_year_slug") String academicYearSlug,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("student_details") List<StudentMarksList> studentMarksLists,
      @JsonProperty("overall_details") List<Overall> overall) {}

  @Builder
  public record StudentMarksList(
      @JsonProperty("student_id") String studentId,
      @JsonProperty("auth_user_id") String studentAuthId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("student_class_roll_number") String studentClassRollNumber,
      @JsonProperty("student_admission_number") String studentAdmissionNumber,
      @JsonProperty("offline_schedule_details")
          List<OfflineTestScheduleResponse> offlineTestScheduleResponse,
      @JsonProperty("total_marks_response") TotalMarksResponse totalMarksResponse) {}

  @Builder
  public record OfflineTestScheduleResponse(
      @JsonProperty("offline_test_schedule_id") Long offlineTestScheduleId,
      @JsonProperty("title") String title,
      @JsonProperty("marks") Object marks,
      @JsonProperty("grade") String grade,
      @JsonProperty("is_report_card") Boolean isReportCard,
      @JsonProperty("is_percentage") Boolean isPercentage) {}

  @Builder
  public record TotalMarksResponse(
      @JsonProperty("total_marks") BigDecimal totalMarks,
      @JsonProperty("grade") String overallGrade) {}

  @Builder
  public record Overall(
      @JsonProperty("offline_test_schedule_id") Long offlineTestScheduleId,
      @JsonProperty("title") String title,
      @JsonProperty("marks") Double marks) {}
}
