package com.wexl.retail.offlinetest.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "offline_test_assessment")
public class OfflineTestAssessment extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "name")
  private String name;

  @Column(name = "slug")
  private String slug;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "offlineTestAssessment", fetch = FetchType.LAZY)
  private List<OfflineTestAssessmentType> offlineTestAssessmentTypes;

  @Column(name = "seq_no")
  private Long seqNo;

  @Column(name = "is_unique")
  private Boolean isUnique;
}
