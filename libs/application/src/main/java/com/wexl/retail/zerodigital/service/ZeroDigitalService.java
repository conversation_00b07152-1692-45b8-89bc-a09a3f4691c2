package com.wexl.retail.zerodigital.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.ChapterResponse;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.content.model.SubTopicResponse;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestRequest;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.dto.TestDefinitionRequest;
import com.wexl.retail.test.school.dto.TestDefinitionsDto;
import com.wexl.retail.test.school.dto.TestQuestionRequest;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.repository.TestDefinitionSectionRepository;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.zerodigital.dto.ZeroDigitalDto;
import com.wexl.retail.zerodigital.model.ZeroDigital;
import com.wexl.retail.zerodigital.model.ZeroDigitalStatus;
import com.wexl.retail.zerodigital.repository.ZeroDigitalRepository;
import jakarta.transaction.Transactional;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ZeroDigitalService {

  private final ZeroDigitalRepository zeroDigitalRepository;

  private final OrgSettingsService orgSettingsService;

  private final ContentService contentService;

  private final TestDefinitionRepository testDefinitionRepository;

  private final ScheduleTestRepository scheduleTestRepository;

  private final ValidationUtils validationUtils;

  private final DateTimeUtil dateTimeUtil;

  private final SectionRepository sectionRepository;

  private final ScheduleTestService scheduleTestService;

  private final StudentRepository studentRepository;

  private final AuthService authService;

  private final TeacherRepository teacherRepository;

  private final TestDefinitionService testDefinitionService;

  private final TestDefinitionSectionRepository testSectionRepository;

  private final TestQuestionRepository testQuestionRepository;

  private final StorageService storageService;

  @Value("${app.contentToken}")
  private String contentBearerToken;

  private static final Integer TOTAL_QUESTION = 15;

  @Transactional
  public void initiateZeroDigital(String orgSlug, List<ZeroDigitalDto.ZdRequest> requests) {
    var organization = orgSettingsService.validateOrganizaiton(orgSlug);

    List<ZeroDigital> zeroDigitalList = new ArrayList<>();

    requests.forEach(
        request -> {
          var chapterResponses =
              validateAndGetChapters(
                  organization.getSlug(),
                  request.board(),
                  request.grade(),
                  request.subject(),
                  request.section());
          if (!chapterResponses.isEmpty()) {
            zeroDigitalList.addAll(
                saveZeroDigitalByChapters(
                    organization.getSlug(), chapterResponses, request.section()));
          }
        });
    zeroDigitalRepository.saveAll(zeroDigitalList);
  }

  private List<ZeroDigital> saveZeroDigitalByChapters(
      String org, List<ChapterResponse> chapterResponses, String sectionUuid) {
    return chapterResponses.stream()
        .map(
            chapterResponse -> {
              var testDefinition =
                  validateTestDefinitionByChapterSlug(chapterResponse.getChapterSlug());
              return buildZeroDigital(chapterResponse, testDefinition, org, sectionUuid);
            })
        .toList();
  }

  private TestDefinition validateTestDefinitionByChapterSlug(String chapterSlug) {
    return testDefinitionRepository
        .findTop1ByTestNameAndOrganizationAndPublishedAtNotNullAndDeletedAtIsNull(
            constructZdSlug(chapterSlug), Constants.WEXL_INTERNAL)
        .orElse(null);
  }

  private TestDefinition validatingTestDefinitionByChapterSlug(String chapterSlug) {
    return testDefinitionRepository
        .findTop1ByTestNameAndOrganization(constructZdSlug(chapterSlug), Constants.WEXL_INTERNAL)
        .orElse(null);
  }

  private List<ChapterResponse> validateAndGetChapters(
      String orgSlug, String board, String grade, String subjectSlug, String section) {

    var chapterResponses =
        contentService.getChaptersByBoardGradeAndSubject(orgSlug, board, grade, subjectSlug);
    var zdChapters =
        zeroDigitalRepository
            .findAllByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuidAndSubjectSlugAndDeletedAtIsNull(
                orgSlug, board, grade, section, subjectSlug)
            .stream()
            .map(ZeroDigital::getChapterSlug)
            .toList();

    if (zdChapters.isEmpty()) {
      return chapterResponses;
    }
    var inactivatedChapters =
        zdChapters.stream()
            .filter(
                chapter ->
                    !chapterResponses.stream()
                        .map(ChapterResponse::getChapterSlug)
                        .toList()
                        .contains(chapter))
            .toList();
    if (!inactivatedChapters.isEmpty()) {
      deleteChapters(orgSlug, inactivatedChapters);
    }
    return chapterResponses.stream()
        .filter(chapter -> !zdChapters.contains(chapter.getChapterSlug()))
        .toList();
  }

  private void deleteChapters(String orgSlug, List<String> chapterSlugs) {
    var zeroDigitals = zeroDigitalRepository.findByOrgSlugAndChapterSlugIn(orgSlug, chapterSlugs);
    zeroDigitalRepository.deleteAll(zeroDigitals);
  }

  private ZeroDigital buildZeroDigital(
      ChapterResponse chapterResponse,
      TestDefinition testDefinition,
      String org,
      String sectionUuid) {
    var section = validateSection(sectionUuid);
    return ZeroDigital.builder()
        .boardSlug(section.getBoardSlug())
        .gradeSlug(chapterResponse.getGradeSlug())
        .gradeName(section.getGradeName())
        .chapterName(chapterResponse.getName())
        .chapterSlug(chapterResponse.getChapterSlug())
        .orgSlug(org)
        .testDefinitionId(Objects.nonNull(testDefinition) ? testDefinition.getId() : null)
        .zdSlug(constructZdSlug(chapterResponse.getChapterSlug()))
        .status(ZeroDigitalStatus.NOT_STARTED)
        .subjectSlug(chapterResponse.getSubjectSlug())
        .subjectName(chapterResponse.getSubjectName())
        .sectionUuid(String.valueOf(section.getUuid()))
        .sectionName(section.getName())
        .build();
  }

  private String constructZdSlug(String chapterSlug) {
    return String.format("zd-%s", chapterSlug);
  }

  private Section validateSection(String sectionUuid) {
    if (sectionUuid == null || sectionUuid.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionUuidNull");
    }
    try {
      return sectionRepository
          .findByUuid(UUID.fromString(sectionUuid))
          .orElseThrow(
              () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound"));
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Invalid UUID format: " + sectionUuid);
    }
  }

  public List<ZeroDigitalDto.ZeroDigitalResponse> getZeroDigital(
      String orgSlug, String boardSlug, String gradeSlug, String sectionUuid, String subjectSlug) {
    List<ZeroDigital> zeroDigitalList =
        (sectionUuid != null)
            ? zeroDigitalRepository
                .findAllByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuidAndSubjectSlugAndDeletedAtIsNull(
                    orgSlug, boardSlug, gradeSlug, sectionUuid, subjectSlug)
            : zeroDigitalRepository
                .findAllByOrgSlugAndBoardSlugAndGradeSlugAndSubjectSlugAndDeletedAtIsNull(
                    orgSlug, boardSlug, gradeSlug, subjectSlug);
    zeroDigitalList.sort(Comparator.comparing(ZeroDigital::getChapterName));

    return zeroDigitalList.stream().map(this::buildZeroDigitalResponse).toList();
  }

  public List<ZeroDigitalDto.ZeroDigitalReportResponse> getComplianceReport(
      String orgSlug, String boardSlug, String gradeSlug, String sectionUuid, String subjectSlug) {
    List<ZeroDigital> zeroDigitalResponse =
        subjectSlug == null
            ? zeroDigitalRepository
                .findAllByOrgSlugAndSectionUuidAndDeletedAtIsNullOrderByChapterName(
                    orgSlug, sectionUuid)
            : zeroDigitalRepository
                .findAllByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuidAndSubjectSlugAndDeletedAtIsNullOrderByChapterName(
                    orgSlug, boardSlug, gradeSlug, sectionUuid, subjectSlug);

    return zeroDigitalResponse.stream()
        .map(this::mapZeroDigitalToResponse)
        .collect(Collectors.toList());
  }

  private ZeroDigitalDto.ZeroDigitalReportResponse mapZeroDigitalToResponse(
      ZeroDigital zeroDigital) {
    Boolean isExamConductedAt = zeroDigital.getExamConductedAt() != null;
    Boolean isWorkSheetIssuedAt = zeroDigital.getWorkSheetIssuedAt() != null;
    Boolean isBadgesIssuedAt = zeroDigital.getBadgesIssuedAt() != null;
    Boolean isWorkSheetsCompleted = zeroDigital.getWorkSheetsCompleted() != null;

    Long examConductedEpochSeconds =
        zeroDigital.getExamConductedAt() != null
            ? DateTimeUtil.convertIso8601ToEpoch(zeroDigital.getExamConductedAt())
            : null;
    Long workSheetIssuedEpochSeconds =
        zeroDigital.getWorkSheetIssuedAt() != null
            ? DateTimeUtil.convertIso8601ToEpoch(zeroDigital.getWorkSheetIssuedAt())
            : null;
    Long badgesIssuedEpochSeconds =
        zeroDigital.getBadgesIssuedAt() != null
            ? DateTimeUtil.convertIso8601ToEpoch(zeroDigital.getBadgesIssuedAt())
            : null;
    Long workSheetCompletedEpochSeconds =
        zeroDigital.getWorkSheetsCompleted() != null
            ? DateTimeUtil.convertIso8601ToEpoch(zeroDigital.getWorkSheetsCompleted())
            : null;

    return ZeroDigitalDto.ZeroDigitalReportResponse.builder()
        .zdId(zeroDigital.getId())
        .chapterSlug(zeroDigital.getChapterSlug())
        .chapterName(zeroDigital.getChapterName())
        .examConductedAt(examConductedEpochSeconds)
        .isExamConductedAt(isExamConductedAt)
        .workSheetIssuedAt(workSheetIssuedEpochSeconds)
        .isWorkSheetIssuedAt(isWorkSheetIssuedAt)
        .badgesIssuedAt(badgesIssuedEpochSeconds)
        .isBadgesIssuedAt(isBadgesIssuedAt)
        .workSheetsCompleted(workSheetCompletedEpochSeconds)
        .isWorkSheetsCompleted(isWorkSheetsCompleted)
        .build();
  }

  private ZeroDigitalDto.ZeroDigitalResponse buildZeroDigitalResponse(ZeroDigital zd) {
    TestDefinition testDefinition = null;
    if (Objects.nonNull(zd.getTestDefinitionId())) {
      testDefinition = validationUtils.validateTestDefinition(zd.getTestDefinitionId());
    }

    return ZeroDigitalDto.ZeroDigitalResponse.builder()
        .zdId(zd.getId())
        .testDefinitionId(Objects.nonNull(testDefinition) ? testDefinition.getId() : null)
        .associatedTest(Objects.nonNull(testDefinition) ? testDefinition.getTestName() : null)
        .testScheduleId(zd.getTestScheduleId())
        .chapterName(zd.getChapterName())
        .chapterSlug(zd.getChapterSlug())
        .gradeName(zd.getGradeName())
        .plannedDate(
            Objects.nonNull(zd.getPlannedDate())
                ? DateTimeUtil.convertIso8601ToEpoch(zd.getPlannedDate())
                : null)
        .completionDate(
            Objects.nonNull(zd.getCompletionDate())
                ? DateTimeUtil.convertIso8601ToEpoch(zd.getCompletionDate())
                : null)
        .subjectName(zd.getSubjectName())
        .status(zd.getStatus())
        .examConductedAt(
            Objects.nonNull(zd.getExamConductedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(zd.getExamConductedAt())
                : null)
        .BadgesIssuedAt(
            Objects.nonNull(zd.getBadgesIssuedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(zd.getBadgesIssuedAt())
                : null)
        .WorkSheetIssuedAt(
            Objects.nonNull(zd.getWorkSheetIssuedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(zd.getWorkSheetIssuedAt())
                : null)
        .workSheetsCompleted(
            Objects.nonNull(zd.getWorkSheetsCompleted())
                ? DateTimeUtil.convertIso8601ToEpoch(zd.getWorkSheetsCompleted())
                : null)
        .build();
  }

  public void updateZeroDigital(String orgSlug, Long zdId, ZeroDigitalDto.ZdUpdateRequest request) {
    var zeroDigital = validateZeroDigital(orgSlug, zdId);
    zeroDigital.setPlannedDate(
        Objects.nonNull(request.plannedDate())
            ? dateTimeUtil.convertEpochToIso8601(request.plannedDate())
            : zeroDigital.getPlannedDate());
    zeroDigital.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    zeroDigital.setExamConductedAt(
        updateZeroDigitalCompliance(request.examCompleted(), zeroDigital.getExamConductedAt()));
    zeroDigital.setBadgesIssuedAt(
        updateZeroDigitalCompliance(request.badgesIssued(), zeroDigital.getBadgesIssuedAt()));
    zeroDigital.setWorkSheetIssuedAt(
        updateZeroDigitalCompliance(request.workSheetIssued(), zeroDigital.getWorkSheetIssuedAt()));
    zeroDigital.setWorkSheetsCompleted(
        updateZeroDigitalCompliance(
            request.workSheetCompleted(), zeroDigital.getWorkSheetsCompleted()));
    zeroDigitalRepository.save(zeroDigital);
  }

  private LocalDateTime updateZeroDigitalCompliance(
      Boolean compliance, LocalDateTime complianceDate) {
    if (Objects.isNull(compliance)) {
      return complianceDate;
    } else if (Boolean.TRUE.equals(compliance)) {
      return LocalDateTime.now();
    }
    return null;
  }

  private ZeroDigital validateZeroDigital(String orgSlug, Long zdId) {
    return zeroDigitalRepository
        .findByIdAndOrgSlug(zdId, orgSlug)
        .orElseThrow(
            () ->
                new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ZeroDigitalNotFound"));
  }

  public void triggerZeroDigital(String orgSlug, Long id) {
    var zeroDigital = validateZeroDigital(orgSlug, id);

    var testDefinition = validationUtils.validateTestDefinition(zeroDigital.getTestDefinitionId());
    try {
      var simpleScheduleTestRequest = buildSimpleScheduleTestRequest(zeroDigital, testDefinition);
      if (simpleScheduleTestRequest.getStudentIds().isEmpty()) {
        log.info(String.format("No students found in section %s", zeroDigital.getSectionName()));
        return;
      }
      scheduleTestService.scheduleTest(simpleScheduleTestRequest);
      zeroDigital.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
      zeroDigital.setStatus(ZeroDigitalStatus.IN_PROGRESS);
      zeroDigitalRepository.save(zeroDigital);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private SimpleScheduleTestRequest buildSimpleScheduleTestRequest(
      ZeroDigital zeroDigital, TestDefinition testDefinition) {
    var students =
        studentRepository.getStudentsBySectionUuidsAndOrgSlug(
            List.of(UUID.fromString(zeroDigital.getSectionUuid())), zeroDigital.getOrgSlug());

    var studentIds =
        students.stream().map(Student::getUserInfo).map(User::getId).collect(Collectors.toSet());

    var today = DateTimeUtil.convertIso8601ToEpoch(LocalDateTime.now());
    var oneYear = DateTimeUtil.convertIso8601ToEpoch(LocalDateTime.now().plusYears(1));

    return SimpleScheduleTestRequest.builder()
        .message("All the Best")
        .testDefinitionId(testDefinition.getId())
        .startDate(today)
        .endDate(oneYear)
        .duration(60)
        .metadata(
            ScheduleTestMetadata.builder()
                .board(zeroDigital.getBoardSlug())
                .grade(zeroDigital.getGradeSlug())
                .sections(List.of(zeroDigital.getSectionUuid()))
                .build())
        .studentIds(studentIds)
        .build();
  }

  private ZeroDigital validateZeroDigitalByChapterSlugAndOrgSlug(
      Long zdId, String chapterSlug, String orgSlug) {
    return zeroDigitalRepository
        .findByIdAndChapterSlugAndOrgSlug(zdId, chapterSlug, orgSlug)
        .orElseThrow(
            () ->
                new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ZeroDigitalNotFound"));
  }

  public void createZeroDigitalTest(String orgSlug, Long zdId, String chapterSlug) {
    var zeroDigital = validateZeroDigitalByChapterSlugAndOrgSlug(zdId, chapterSlug, orgSlug);
    if (Objects.nonNull(zeroDigital.getTestDefinitionId())) {
      return;
    }
    var testDefinition = validatingTestDefinitionByChapterSlug(zeroDigital.getChapterSlug());
    if (Objects.nonNull(testDefinition)) {
      zeroDigitalRepository.saveAll(
          updateZeroDigitalByTestDefinition(testDefinition, zeroDigital.getZdSlug()));
      return;
    }
    var newTestDefinition =
        testDefinitionService.createTestDefinition(
            buildTestDefinitionRequest(zeroDigital), null, Constants.WEXL_INTERNAL);
    updateTestSectionAndQuestion(zeroDigital, newTestDefinition, chapterSlug);
    zeroDigitalRepository.saveAll(
        updateZeroDigitalByTestDefinition(newTestDefinition, zeroDigital.getZdSlug()));
  }

  private TestDefinitionRequest buildTestDefinitionRequest(ZeroDigital zeroDigital) {

    var teacher = teacherRepository.findByUserInfo(authService.getTeacherDetails()).orElseThrow();
    return TestDefinitionRequest.builder()
        .testName(zeroDigital.getZdSlug())
        .testType(TestType.MOCK_TEST)
        .boardSlug(zeroDigital.getBoardSlug())
        .gradeSlug(zeroDigital.getGradeSlug())
        .subjectSlug(zeroDigital.getSubjectSlug())
        .active(true)
        .teacherId(teacher.getId())
        .noOfQuestions(TOTAL_QUESTION)
        .isAutoEnabled(true)
        .build();
  }

  private void updateTestSectionAndQuestion(
      ZeroDigital zeroDigital, TestDefinition testDefinition, String chapter) {
    testDefinitionService.createTestDefinitionSection(
        testDefinition.getId(),
        TestDefinitionsDto.TestDefinitionSectionRequest.builder()
            .name(chapter)
            .noOfQuestions(Long.valueOf(TOTAL_QUESTION))
            .build());
    var testSections = testSectionRepository.findByTestDefinitionOrderById(testDefinition);
    var testQuestionRequest =
        TestDefinitionRequest.builder()
            .questions(buildTestQuestionsRequest(zeroDigital, testSections.getFirst()))
            .build();
    testDefinitionService.addQuestionsToSection(
        testSections.getFirst().getTestDefinition().getId(),
        testQuestionRequest,
        testSections.getFirst().getId());
    var testDefinitionSection = testSections.getFirst();
    testDefinition.setTestDefinitionSections(testSections);
    var testQuestions =
        testQuestionRepository.findByTestDefinitionSectionOrderById(testDefinitionSection);
    testDefinitionSection.setTestQuestions(testQuestions);
    var newTestDef = testDefinitionRepository.save(testDefinition);
    testDefinitionService.publishTestDefinitionById(
        newTestDef.getId(), true, contentBearerToken, true);
  }

  private List<ZeroDigital> updateZeroDigitalByTestDefinition(
      TestDefinition testDefinition, String zdSlug) {
    var zeroDigitalList = zeroDigitalRepository.findByZdSlugAndTestDefinitionIdIsNull(zdSlug);
    return zeroDigitalList.stream()
        .peek(
            zd -> {
              zd.setTestDefinitionId(testDefinition.getId());
              zd.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
            })
        .toList();
  }

  private List<TestQuestionRequest> buildTestQuestionsRequest(
      ZeroDigital zeroDigital, TestDefinitionSection testSection) {

    var subTopicResponses = getSubtopics(zeroDigital.getChapterSlug());
    if (subTopicResponses.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.subtopicsNotFound");
    }
    var subtopicSlugs = subTopicResponses.stream().map(SubTopicResponse::getSlug).toList();

    var questionResponse =
        contentService.getQuestionsBySubjectAndSubtopics(
            Constants.WEXL_INTERNAL, QuestionType.MCQ, zeroDigital.getSubjectSlug(), subtopicSlugs);

    var questions = getAllSubtopicsCoveredQuestions(questionResponse, subtopicSlugs);

    return questions.stream()
        .map(
            question ->
                TestQuestionRequest.builder()
                    .type(QuestionType.MCQ.name())
                    .question(question.question())
                    .chapterSlug(question.chapterSlug())
                    .marks(1)
                    .subtopicSlug(question.subtopicSlug())
                    .testDefinitionSectionId(testSection.getId())
                    .questionUuid(question.uuid())
                    .subjectSlug(zeroDigital.getSubjectSlug())
                    .testDefinition(testSection.getTestDefinition().getId())
                    .mcqAnswer(question.mcq().answer())
                    .build())
        .toList();
  }

  private List<QuestionDto.Question> getAllSubtopicsCoveredQuestions(
      QuestionDto.SearchQuestionResponse questionResponse, List<String> subtopicSlugs) {
    Map<String, List<QuestionDto.Question>> topicQuestionMap =
        questionResponse.questions().stream()
            .collect(Collectors.groupingBy(QuestionDto.Question::subtopicSlug));
    int questionCount = Math.round((float) TOTAL_QUESTION / subtopicSlugs.size());
    List<QuestionDto.Question> finalQuestions = new ArrayList<>();
    List<QuestionDto.Question> tempList = new ArrayList<>();
    topicQuestionMap.forEach(
        (topic, questions) -> {
          var questionList = questions.stream().limit(questionCount).toList();
          finalQuestions.addAll(questionList);
          if (questions.size() > questionCount) {
            questions.removeAll(questionList);
            tempList.addAll(questions);
          }
        });
    if (!tempList.isEmpty() && finalQuestions.size() < TOTAL_QUESTION) {
      Collections.shuffle(tempList);
      var remainingQuestions =
          tempList.stream().limit(TOTAL_QUESTION - finalQuestions.size()).toList();
      finalQuestions.addAll(remainingQuestions);
    }
    return finalQuestions.stream().limit(TOTAL_QUESTION).toList();
  }

  private List<SubTopicResponse> getSubtopics(String chapterSlug) {
    var subTopicResponses =
        contentService.getSubTopicsByChapter(Constants.WEXL_INTERNAL, chapterSlug);
    var mlpSubtopics =
        subTopicResponses.stream()
            .filter(
                topicResponse ->
                    (StringUtils.containsAnyIgnoreCase(topicResponse.getName(), "mlp", "part")))
            .toList();
    return mlpSubtopics.isEmpty() ? subTopicResponses : mlpSubtopics;
  }

  public void addZeroDigitalQuestions(
      String orgSlug,
      long testDefinitionSectionId,
      TestDefinitionsDto.ZeroDigitalQuestionRequest request) {
    var testDefinitionSection =
        testDefinitionService.getTestDefinitionSection(testDefinitionSectionId);

    List<TestQuestion> questionList =
        request.chapterSlugs().stream()
            .map(
                chapter -> {
                  var zeroDigital = validateZeroDigitalByChapterSlug(orgSlug, chapter);
                  var internalTest =
                      validationUtils.validateTestDefinition(zeroDigital.getTestDefinitionId());
                  var testQuestions =
                      internalTest.getTestDefinitionSections().get(0).getTestQuestions();
                  var testQuestions1 =
                      testQuestions.stream()
                          .map(
                              tq -> {
                                var testQuestion = buildTestQuestion(tq);
                                testQuestion.setTestDefinitionSection(testDefinitionSection);
                                return testQuestion;
                              })
                          .toList();
                  return testDefinitionService.validateTestQuestions(
                      testQuestions1, testDefinitionSectionId);
                })
            .flatMap(Collection::stream)
            .toList();
    testQuestionRepository.saveAll(questionList);
  }

  private ZeroDigital validateZeroDigitalByChapterSlug(String org, String chapterSlug) {
    var zeroDigital =
        zeroDigitalRepository.findByOrgSlugAndChapterSlugIn(
            org, Collections.singletonList(chapterSlug));
    if (Objects.isNull(zeroDigital.getFirst().getTestDefinitionId())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestNotAssociated");
    }
    return zeroDigital.getFirst();
  }

  private TestQuestion buildTestQuestion(TestQuestion question) {
    return TestQuestion.builder()
        .chapterSlug(question.getChapterSlug())
        .subtopicSlug(question.getSubtopicSlug())
        .subjectSlug(question.getSubjectSlug())
        .negativeMarks(question.getNegativeMarks())
        .spchAnswer(question.getSpchAnswer())
        .natAnswer(question.getNatAnswer())
        .marks(question.getMarks())
        .questionUuid(question.getQuestionUuid())
        .type(question.getType())
        .testDefinitionSection(question.getTestDefinitionSection())
        .mcqAnswer(question.getMcqAnswer())
        .msqAnswer(question.getMsqAnswer())
        .pbqAnswers(question.getPbqAnswers())
        .natAnswer(question.getNatAnswer())
        .yesNo(question.getYesNo())
        .amcqAnswer(question.getAmcqAnswer())
        .fbqAnswer(question.getFbqAnswer())
        .subjectiveAnswer(question.getSubjectiveAnswer())
        .build();
  }

  public void linkTestToZeroDigital(String orgSlug, Long id, long testScheduleId) {

    var scheduleTests =
        scheduleTestRepository
            .findById(testScheduleId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestSchedule"));

    var zeroDigital = validateZeroDigital(orgSlug, id);
    zeroDigital.setTestScheduleId(scheduleTests.getId());
    zeroDigital.setStatus(ZeroDigitalStatus.COMPLETED);
    zeroDigital.setCompletionDate(LocalDateTime.now());
    zeroDigital.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    zeroDigitalRepository.save(zeroDigital);
  }

  public void generateZeroDigitalTests(int limit) {
    Pageable pageable = PageRequest.of(0, limit);
    final List<ZeroDigital> allByTestDefinitionIdIsNull =
        zeroDigitalRepository.findAllByTestDefinitionIdIsNull(pageable);
    allByTestDefinitionIdIsNull.forEach(
        zeroDigital -> {
          try {
            createZeroDigitalTest(
                zeroDigital.getOrgSlug(), zeroDigital.getId(), zeroDigital.getChapterSlug());
          } catch (Exception ex) {
            log.error("Unable to create Test for ZeroDigital {}", zeroDigital.getChapterSlug(), ex);
          }
        });
  }

  @Async
  public void updateZeroDigitalStatus(Exam exam) {
    try {
      var testDefinition = exam.getScheduleTest().getTestDefinition();

      var testQuestions =
          testDefinition.getTestDefinitionSections().stream()
              .map(TestDefinitionSection::getTestQuestions)
              .flatMap(Collection::stream)
              .toList();

      var chapters =
          testQuestions.stream().map(TestQuestion::getChapterSlug).collect(Collectors.toSet());

      var zeroDigitals =
          zeroDigitalRepository.findByOrgSlugAndChapterSlugInAndStatus(
              testDefinition.getOrganization(),
              new ArrayList<>(chapters),
              ZeroDigitalStatus.NOT_STARTED);

      zeroDigitals.stream()
          .filter(zd -> Objects.nonNull(zd.getTestDefinitionId()))
          .forEach(
              zd -> {
                zd.setTestScheduleId(exam.getScheduleTest().getId());
                zd.setCompletionDate(LocalDateTime.now());
                zd.setStatus(ZeroDigitalStatus.COMPLETED);
                zd.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
              });
      zeroDigitalRepository.saveAll(zeroDigitals);
    } catch (Exception e) {
      log.error("There was an error while auto associate in ZERO DIGITAL");
    }
  }
}
