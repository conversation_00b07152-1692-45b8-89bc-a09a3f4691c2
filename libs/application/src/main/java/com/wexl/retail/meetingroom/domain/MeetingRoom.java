package com.wexl.retail.meetingroom.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.schedules.domain.MeetingType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@Table(name = "meeting_rooms")
@AllArgsConstructor
public class MeetingRoom extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "org_id")
  private Organization organization;

  private String orgSlug;

  @Enumerated(EnumType.STRING)
  private MeetingType type;

  @Column(name = "host_link")
  private String hostLink;

  @Column(name = "join_link")
  private String joinLink;

  @Column(name = "display_name")
  private String displayName;
}
