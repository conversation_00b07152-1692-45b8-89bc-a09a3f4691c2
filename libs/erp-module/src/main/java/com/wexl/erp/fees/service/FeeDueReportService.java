package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    List<FeeDto.FeeDueReportResponse> reportData = generateFeeDueReport(orgSlug, request);
    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    generateCsvResponse(reportData, response, reportType, request);
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request) {

    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    return students.stream()
        .map(
            student -> {
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, feeHeadsByStudent.get(student));
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  @Async
  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";
    return switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };
  }

  @Async
  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getDescription())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv");
    response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

    List<List<String>> csvBody = buildCsvBody(reportData, request);
    List<String> csvHeaders = csvBody.isEmpty() ? List.of() : csvBody.remove(0);

    CsvUtils.generateCsv(csvHeaders.toArray(new String[0]), csvBody, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildCsvBody(List<FeeDto.FeeDueReportResponse> reportData, FeeDto.FeeDueReportRequest request) {
    List<List<String>> csvBody = new ArrayList<>();

    DynamicHeaderStructure headerStructure = generateDynamicHeaders(reportData, request);

    csvBody.add(headerStructure.headers());

    for (FeeDto.FeeDueReportResponse report : reportData) {
      List<String> row = buildDataRow(report, headerStructure);
      csvBody.add(row);
    }

    return csvBody;
  }

  private DynamicHeaderStructure generateDynamicHeaders(List<FeeDto.FeeDueReportResponse> reportData, FeeDto.FeeDueReportRequest request) {
    List<String> standardMonths = List.of("APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "JAN", "FEB", "MAR");
    List<String> feeGroupTypes = (request.feeGroupTypes() != null && !request.feeGroupTypes().isEmpty())
        ? request.feeGroupTypes()
        : discoverFeeGroupTypes(reportData);

    List<String> headers = new ArrayList<>();
    List<String> headerKeys = new ArrayList<>();

    headers.addAll(List.of("Student Name", "Admission Number", "Roll Number", "Section", "Date of Admission"));
    headerKeys.addAll(List.of("studentName", "admissionNumber", "rollNumber", "sectionName", "dateOfAdmission"));

    for (String feeGroupType : feeGroupTypes) {
      for (String month : standardMonths) {
        headers.add(feeGroupType + " - " + month);
        headerKeys.add(feeGroupType + "_" + month);
      }

      headers.add(feeGroupType + " - Total Fee");
      headers.add(feeGroupType + " - Concession Amount");
      headers.add(feeGroupType + " - Total Paid");
      headers.add(feeGroupType + " - Total Due");

      headerKeys.add(feeGroupType + "_TotalFee");
      headerKeys.add(feeGroupType + "_ConcessionAmount");
      headerKeys.add(feeGroupType + "_TotalPaid");
      headerKeys.add(feeGroupType + "_TotalDue");
    }

    headers.add("Overall Discount Amount");
    headers.add("Overall Total Due");
    headerKeys.add("overallDiscountAmount");
    headerKeys.add("overallTotalDue");

    return new DynamicHeaderStructure(headers, headerKeys, feeGroupTypes, standardMonths);
  }

  private List<String> discoverFeeGroupTypes(List<FeeDto.FeeDueReportResponse> reportData) {
    Set<String> discoveredTypes = new TreeSet<>();
    for (FeeDto.FeeDueReportResponse report : reportData) {
      for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
        discoveredTypes.add(detail.feeTypeName());
      }
    }
    return new ArrayList<>(discoveredTypes);
  }

  private List<String> buildDataRow(FeeDto.FeeDueReportResponse report, DynamicHeaderStructure headerStructure) {
    List<String> row = new ArrayList<>();

    row.add(report.studentName());
    row.add(report.admissionNumber());
    row.add(report.rollNumber());
    row.add(report.sectionName());
    row.add(report.dateOfAdmission());

    Map<String, Map<String, Double>> feeDataByGroupAndMonth = buildFeeDataMaps(report);

    for (String feeGroupType : headerStructure.feeGroupTypes()) {
      Map<String, Double> feeGroupData = feeDataByGroupAndMonth.getOrDefault(feeGroupType, new HashMap<>());

      for (String month : headerStructure.standardMonths()) {
        Double monthlyAmount = feeGroupData.getOrDefault(month, 0.0);
        row.add(String.format("%.0f", monthlyAmount));
      }

      double totalFee = feeGroupData.values().stream().mapToDouble(Double::doubleValue).sum();
      double concessionAmount = 0.0;
      double totalPaid = calculateTotalPaidForFeeGroup(report, feeGroupType);
      double totalDue = totalFee - totalPaid;

      row.add(String.format("%.0f", totalFee));
      row.add(String.format("%.0f", concessionAmount));
      row.add(String.format("%.0f", totalPaid));
      row.add(String.format("%.0f", totalDue));
    }

    row.add(String.format("%.0f", report.discountAmount()));
    row.add(String.format("%.0f", report.totalDueAmount()));

    return row;
  }

  private Map<String, Map<String, Double>> buildFeeDataMaps(FeeDto.FeeDueReportResponse report) {
    Map<String, Map<String, Double>> feeDataByGroupAndMonth = new HashMap<>();

    for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
      String feeGroup = detail.feeTypeName();
      String month = (detail.month() != null && !detail.month().isEmpty()) ? detail.month() : "TOTAL";

      feeDataByGroupAndMonth
          .computeIfAbsent(feeGroup, k -> new HashMap<>())
          .merge(month, detail.balanceAmount(), Double::sum);
    }

    return feeDataByGroupAndMonth;
  }

  private double calculateTotalPaidForFeeGroup(FeeDto.FeeDueReportResponse report, String feeGroupType) {
    return report.feeDetails().stream()
        .filter(detail -> feeGroupType.equals(detail.feeTypeName()))
        .mapToDouble(detail -> detail.paidAmount() != null ? detail.paidAmount() : 0.0)
        .sum();
  }

  private record DynamicHeaderStructure(
      List<String> headers,
      List<String> headerKeys,
      List<String> feeGroupTypes,
      List<String> standardMonths
  ) {}
}
